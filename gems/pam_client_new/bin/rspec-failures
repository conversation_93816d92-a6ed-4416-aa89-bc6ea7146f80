#!/usr/bin/env bash

# Script to run only failed RSpec examples
# Usage: 
#   ./bin/rspec-failures                    # Run only failed specs
#   ./bin/rspec-failures --all             # Run all specs and track failures
#   ./bin/rspec-failures --clear           # Clear failure tracking

set -e

cd "$(dirname "$0")/.."

case "${1:-}" in
  --all)
    echo "Running all specs and tracking failures..."
    bundle exec rspec --format documentation
    ;;
  --clear)
    echo "Clearing failure tracking..."
    rm -f spec/examples.txt
    echo "Failure tracking cleared."
    ;;
  --help|-h)
    echo "Usage:"
    echo "  ./bin/rspec-failures           # Run only failed specs"
    echo "  ./bin/rspec-failures --all     # Run all specs and track failures"
    echo "  ./bin/rspec-failures --clear   # Clear failure tracking"
    echo "  ./bin/rspec-failures --help    # Show this help"
    ;;
  *)
    if [ -f "spec/examples.txt" ]; then
      echo "Running only failed specs..."
      bundle exec rspec --only-failures --format documentation
    else
      echo "No failure tracking file found. Running all specs to establish baseline..."
      bundle exec rspec --format documentation
    fi
    ;;
esac
