Running only failed specs...
Run options: include {last_run_status: "failed"}

PamClient::BaseAgency
  generational methods
    for an agency
      that is new
        return the correct values (FAILED - 1)

PamClient::Dhx
  validations
    is expected to belong to user required: false (FAILED - 2)
    is expected to validate that :user cannot be empty/falsy (FAILED - 3)

generational methods
  a new object
    return the correct values (FAILED - 4)
  with no children
    return the correct values (FAILED - 5)
  with children
    return the correct values (FAILED - 6)

PamClient::Concerns::HasNamespacedName
  when included
    #as_json
      copies '[table_name]_name' to 'name' (FAILED - 7)

PamClient::RegistrationDropdownValues
  #value_object
    behaves like a_dropdown_values_mixin_value_object
      type == "agency"
        user_id belongs to an ae
          returns the ids and names of agencies on which the user has aor assignments
          returns an empty array if the user has no aor assignments
      type == "advertiser"
        raises an InvalidOptionsError if no user_id is given
        raises an InvalidOptionsError if no agency_id is given
        user_id belongs to a vp
          returns the ids and names of advertisers on which the user has registration assignments (FAILED - 8)
          returns an empty array if the user has no registration assignments (FAILED - 9)
        user_id belongs to an ae
          returns the ids and names of advertisers on which the user has aor assignments (FAILED - 10)
          returns an empty array if the user has no aor assignments (FAILED - 11)
      type == "buying_ae"
        raises an InvalidOptionsError if no agency_id is given (FAILED - 12)
        raises an InvalidOptionsError if no advertiser_id is given (FAILED - 13)
      type == "client_ae"
        raises an InvalidOptionsError if no agency_id is given (FAILED - 14)
        raises an InvalidOptionsError if no advertiser_id is given (FAILED - 15)
    type == agency
      raises an InvalidOptionsError if no property_id is given (FAILED - 16)
    type == "advertiser"
      raises an InvalidOptionsError if no property_id is given (FAILED - 17)
    type == "buying_ae"
      raises an InvalidOptionsError if no property_id is given (FAILED - 18)
    type == "client_ae"
      raises an InvalidOptionsError if no property_id is given (FAILED - 19)
    type == "property"
      raises an InvalidOptionsError if no user_id is given (FAILED - 20)
      user_id belongs to a vp
        returns the ids and names of properties on which the user has registration assignments (FAILED - 21)
        returns an empty array if the user has no registration assignments (FAILED - 22)
      user_id belongs to an ae
        returns the ids and names of properties on which the user has aor assignments (FAILED - 23)
        returns an empty array if the user has no registration or aor assignments (FAILED - 24)
      type == "buying_ae"
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 25)
      type == "client_ae"
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 26)

PamClient::Concerns::HasAdditionalFilters
  relevant_additional_filters
    returns only those filters that have values (FAILED - 27)

PamClient::Reports::MicrostrategyReportTag
  validations
    should validate duplicate records (FAILED - 28)

PamClient::Reports::SavedTemplate
  #soft_destroy
    sets active to false (FAILED - 29)
    destroys associated shared templates (FAILED - 30)
  #shareable_users
    returns active internal users not already having access to template (FAILED - 31)
    does not return external users (FAILED - 32)
    does not return inactive users (FAILED - 33)
    does not return user associated to template (FAILED - 34)
    deoes not return existing user that has share access to template (FAILED - 35)
  .prompt_xml_for
    returns prompt_xml generated for the template (FAILED - 36)
    contains the appended sso_id tag when provided (FAILED - 37)

PamClient::Reports::TrackPulledReport
  .recently_ran
    returns reports recently ran for a user (FAILED - 38)
    filters by dashboard (FAILED - 39)
    uniqueness
      returns unique reports (FAILED - 40)
    with saved templates
      returns unique report/template combinations (FAILED - 41)

PamClient::Reports::UserSavedTemplatesView
  .for_user
    returns only saved_templates for reports the user has access to (FAILED - 42)

PamClient::ShiftRequestEvent
  sorting shift request events
    should sort CREATED to the top and COMPLETED to the bottom (FAILED - 43)

PamClient::SpecialEventsDropdownValues
  .new
    behaves like a_new_dropdown_values_mixin_object
      raises an InvalidOptionsError if an invalid type is given (FAILED - 44)
      raises an InvalidOptionsError if no type is given (FAILED - 45)
  .value_object
    type == "advertiser"
      raises an InvalidOptionsError if no user_id is given (FAILED - 46)
      user_id belongs to a vp
        returns the ids and names of advertisers on which the user has registration assignments (FAILED - 47)
        returns an empty array if the user has no registration assignments (FAILED - 48)
      user_id belongs to an ae
        returns the ids and names of advertisers on which the user has aor assignments (FAILED - 49)
        returns an empty array if the user has no aor assignments (FAILED - 50)
    type == "agency"
      raises an InvalidOptionsError if no user_id is given (FAILED - 51)
      raises an InvalidOptionsError if no advertiser_id is given (FAILED - 52)
      user_id belongs to a vp
        returns the ids and names of agencies on which the user has registration assignments (FAILED - 53)
        returns an empty array if the user has no registration assignments (FAILED - 54)
      user_id belongs to an ae
        returns the ids and names of agencies on which the user has aor assignments (FAILED - 55)
        returns an empty array if the user has no aor assignments (FAILED - 56)
    type == "buying_ae"
      without selected_id
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 57)
      with selected_id
        returns the ids and names of actives aes, with the selected record being the supplied selected_id (FAILED - 58)
    type == "client_ae"
      without selected_id
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 59)
      with selected_id
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 60)

Rails 8 Compatibility Tests
  Fixture Loading Behavior
    when fixtures are actually loaded
      should load fixtures successfully (FAILED - 61)
  Test Environment Setup Differences
    should show database cleaner configuration (FAILED - 62)

Failures:

  1) PamClient::BaseAgency generational methods for an agency that is new return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/base_agency_spec.rb:106:in 'block (5 levels) in <module:PamClient>'

  2) PamClient::Dhx validations is expected to belong to user required: false
     Failure/Error: it { is_expected.to belong_to(:user) }
       Expected PamClient::Dhx to have a belongs_to association called user (PamClient::Dhx does not have a app_user_id foreign key.)
     # ./spec/pam_client/dhx_spec.rb:6:in 'block (3 levels) in <module:PamClient>'

  3) PamClient::Dhx validations is expected to validate that :user cannot be empty/falsy
     Failure/Error: it { should validate_presence_of(:user) }

     ActiveModel::MissingAttributeError:
       can't write unknown attribute `app_user_id`
     # ./spec/pam_client/dhx_spec.rb:11:in 'block (3 levels) in <module:PamClient>'

  4) generational methods a new object return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  5) generational methods with no children return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:25:in 'block (3 levels) in <module:PamClient>'

  6) generational methods with children return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:36:in 'block (3 levels) in <module:PamClient>'

  7) PamClient::Concerns::HasNamespacedName when included #as_json copies '[table_name]_name' to 'name'
     Failure/Error: expect(record.as_json).to eq({ 'singular_name' => 'a value', 'name' => 'a value' })

       expected: {"name" => "a value", "singular_name" => "a value"}
            got: {"singular_name" => "a value"}

       (compared using ==)

       Diff:
       @@ -1,2 +1 @@
       -"name" => "a value",
        "singular_name" => "a value",
     # ./spec/pam_client/has_namespaced_name_spec.rb:37:in 'block (4 levels) in <module:PamClient>'

  8) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to a vp returns the ids and names of advertisers on which the user has registration assignments
     Failure/Error: let!(:gs2) { create(:geo_state, location: loc2) }

     ActiveRecord::ValueTooLong:
       OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
       Help: https://docs.oracle.com/error-help/db/ora-12899/
     Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
     # stmt.c:267:in oci8lib_340.bundle
     # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:7:in 'block (2 levels) in <top (required)>'
     # ------------------
     # --- Caused by: ---
     # OCIError:
     #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
     #   Help: https://docs.oracle.com/error-help/db/ora-12899/
     #   stmt.c:267:in oci8lib_340.bundle

  9) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to a vp returns an empty array if the user has no registration assignments
     Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

     ActiveRecord::ValueTooLong:
       OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
       Help: https://docs.oracle.com/error-help/db/ora-12899/
     Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
     # stmt.c:267:in oci8lib_340.bundle
     # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
     # ------------------
     # --- Caused by: ---
     # OCIError:
     #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
     #   Help: https://docs.oracle.com/error-help/db/ora-12899/
     #   stmt.c:267:in oci8lib_340.bundle

  10) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to an ae returns the ids and names of advertisers on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  11) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to an ae returns an empty array if the user has no aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  12) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "buying_ae" raises an InvalidOptionsError if no agency_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  13) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "buying_ae" raises an InvalidOptionsError if no advertiser_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  14) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "client_ae" raises an InvalidOptionsError if no agency_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  15) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "client_ae" raises an InvalidOptionsError if no advertiser_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  16) PamClient::RegistrationDropdownValues#value_object type == agency raises an InvalidOptionsError if no property_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  17) PamClient::RegistrationDropdownValues#value_object type == "advertiser" raises an InvalidOptionsError if no property_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  18) PamClient::RegistrationDropdownValues#value_object type == "buying_ae" raises an InvalidOptionsError if no property_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  19) PamClient::RegistrationDropdownValues#value_object type == "client_ae" raises an InvalidOptionsError if no property_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  20) PamClient::RegistrationDropdownValues#value_object type == "property" raises an InvalidOptionsError if no user_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  21) PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to a vp returns the ids and names of properties on which the user has registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  22) PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to a vp returns an empty array if the user has no registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  23) PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to an ae returns the ids and names of properties on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  24) PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to an ae returns an empty array if the user has no registration or aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  25) PamClient::RegistrationDropdownValues#value_object type == "property" type == "buying_ae" returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  26) PamClient::RegistrationDropdownValues#value_object type == "property" type == "client_ae" returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  27) PamClient::Concerns::HasAdditionalFilters relevant_additional_filters returns only those filters that have values
      Failure/Error: let(:form) {Reports::MicrostrategyForm.new}

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/has_additional_filters_spec.rb:7:in 'block (3 levels) in <module:PamClient>'
      # ./spec/pam_client/reports/has_additional_filters_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  28) PamClient::Reports::MicrostrategyReportTag validations should validate duplicate records
      Failure/Error: mstr_report_tag_2 = MicrostrategyReportTag.new(tag: mstr_report_tag_1.tag, report: mstr_report_tag_1.report)

      ActiveModel::UnknownAttributeError:
        unknown attribute 'tag' for PamClient::Reports::MicrostrategyReportTag.
      # ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:18:in 'block (3 levels) in <module:Reports>'
      # ------------------
      # --- Caused by: ---
      # NoMethodError:
      #   undefined method 'tag=' for an instance of PamClient::Reports::MicrostrategyReportTag
      #   ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  29) PamClient::Reports::SavedTemplate#soft_destroy sets active to false
      Failure/Error: let(:saved_template_with_share) { create(:saved_template) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:17:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  30) PamClient::Reports::SavedTemplate#soft_destroy destroys associated shared templates
      Failure/Error: let(:saved_template_with_share) { create(:saved_template) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:17:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  31) PamClient::Reports::SavedTemplate#shareable_users returns active internal users not already having access to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  32) PamClient::Reports::SavedTemplate#shareable_users does not return external users
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  33) PamClient::Reports::SavedTemplate#shareable_users does not return inactive users
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  34) PamClient::Reports::SavedTemplate#shareable_users does not return user associated to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  35) PamClient::Reports::SavedTemplate#shareable_users deoes not return existing user that has share access to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  36) PamClient::Reports::SavedTemplate.prompt_xml_for returns prompt_xml generated for the template
      Failure/Error: let!(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:69:in 'block (3 levels) in <module:Reports>'

  37) PamClient::Reports::SavedTemplate.prompt_xml_for contains the appended sso_id tag when provided
      Failure/Error: let!(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:69:in 'block (3 levels) in <module:Reports>'

  38) PamClient::Reports::TrackPulledReport.recently_ran returns reports recently ran for a user
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  39) PamClient::Reports::TrackPulledReport.recently_ran filters by dashboard
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  40) PamClient::Reports::TrackPulledReport.recently_ran uniqueness returns unique reports
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  41) PamClient::Reports::TrackPulledReport.recently_ran with saved templates returns unique report/template combinations
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  42) PamClient::Reports::UserSavedTemplatesView.for_user returns only saved_templates for reports the user has access to
      Failure/Error: mstr_form = create(:microstrategy_form, microstrategy_report: report)

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/user_saved_templates_view_spec.rb:14:in 'block (3 levels) in <module:Reports>'

  43) PamClient::ShiftRequestEvent sorting shift request events should sort CREATED to the top and COMPLETED to the bottom
      Failure/Error: expect(ary).to eq(expected)

        expected: [#<PamClient::ShiftRequestEvent shift_request_event_id: 371, shift_request_event_name: "Shift Creatio...:12:44.000000000 +0000", updated_at: "2025-07-19 14:12:44.000000000 +0000", last_updated_by_id: -1>]
             got: [#<PamClient::ShiftRequestEvent shift_request_event_id: 372, shift_request_event_name: "Email Sent", ...:12:44.000000000 +0000", updated_at: "2025-07-19 14:12:44.000000000 +0000", last_updated_by_id: -1>]

        (compared using ==)

        Diff:
        @@ -1,3 +1,3 @@
        -[#<PamClient::ShiftRequestEvent shift_request_event_id: 371, shift_request_event_name: "Shift Creation", previous_event: nil, created_at: "2025-07-19 14:12:44.000000000 +0000", updated_at: "2025-07-19 14:12:44.000000000 +0000", last_updated_by_id: -1>,
        - #<PamClient::ShiftRequestEvent shift_request_event_id: 372, shift_request_event_name: "Email Sent", previous_event: nil, created_at: "2025-07-19 14:12:44.000000000 +0000", updated_at: "2025-07-19 14:12:44.000000000 +0000", last_updated_by_id: -1>,
        +[#<PamClient::ShiftRequestEvent shift_request_event_id: 372, shift_request_event_name: "Email Sent", previous_event: nil, created_at: "2025-07-19 14:12:44.000000000 +0000", updated_at: "2025-07-19 14:12:44.000000000 +0000", last_updated_by_id: -1>,
        + #<PamClient::ShiftRequestEvent shift_request_event_id: 371, shift_request_event_name: "Shift Creation", previous_event: nil, created_at: "2025-07-19 14:12:44.000000000 +0000", updated_at: "2025-07-19 14:12:44.000000000 +0000", last_updated_by_id: -1>,
          #<PamClient::ShiftRequestEvent shift_request_event_id: 370, shift_request_event_name: "Shift Completed", previous_event: nil, created_at: "2025-07-19 14:12:44.000000000 +0000", updated_at: "2025-07-19 14:12:44.000000000 +0000", last_updated_by_id: -1>]
      # ./spec/pam_client/shift_request_event_spec.rb:16:in 'block (3 levels) in <module:PamClient>'

  44) PamClient::SpecialEventsDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if an invalid type is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_new_dropdown_values_mixin_object" called from ./spec/pam_client/special_events_dropdown_values_spec.rb:45
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  45) PamClient::SpecialEventsDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if no type is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_new_dropdown_values_mixin_object" called from ./spec/pam_client/special_events_dropdown_values_spec.rb:45
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  46) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" raises an InvalidOptionsError if no user_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  47) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to a vp returns the ids and names of advertisers on which the user has registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  48) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to a vp returns an empty array if the user has no registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  49) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to an ae returns the ids and names of advertisers on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  50) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to an ae returns an empty array if the user has no aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  51) PamClient::SpecialEventsDropdownValues.value_object type == "agency" raises an InvalidOptionsError if no user_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  52) PamClient::SpecialEventsDropdownValues.value_object type == "agency" raises an InvalidOptionsError if no advertiser_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  53) PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to a vp returns the ids and names of agencies on which the user has registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  54) PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to a vp returns an empty array if the user has no registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  55) PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to an ae returns the ids and names of agencies on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  56) PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to an ae returns an empty array if the user has no aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  57) PamClient::SpecialEventsDropdownValues.value_object type == "buying_ae" without selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  58) PamClient::SpecialEventsDropdownValues.value_object type == "buying_ae" with selected_id returns the ids and names of actives aes, with the selected record being the supplied selected_id
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  59) PamClient::SpecialEventsDropdownValues.value_object type == "client_ae" without selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  60) PamClient::SpecialEventsDropdownValues.value_object type == "client_ae" with selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 3, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  61) Rails 8 Compatibility Tests Fixture Loading Behavior when fixtures are actually loaded should load fixtures successfully
      Failure/Error: expect(sponsorship_types(:sponsorship_type_001)).to be_present

      NoMethodError:
        undefined method 'sponsorship_types' for #<RSpec::ExampleGroups::Rails8CompatibilityTests::FixtureLoadingBehavior::WhenFixturesAreActuallyLoaded:0x000000013dbb5a38>
      # ./spec/rails8_compatibility_test_spec.rb:23:in 'block (4 levels) in <top (required)>'

  62) Rails 8 Compatibility Tests Test Environment Setup Differences should show database cleaner configuration
      Failure/Error: puts "DatabaseCleaner strategy: #{DatabaseCleaner.strategy}"

      NoMethodError:
        undefined method 'strategy' for module DatabaseCleaner
      # ./spec/rails8_compatibility_test_spec.rb:131:in 'block (3 levels) in <top (required)>'

Finished in 28.42 seconds (files took 8.91 seconds to load)
66 examples, 62 failures

Failed examples:

rspec ./spec/pam_client/base_agency_spec.rb:105 # PamClient::BaseAgency generational methods for an agency that is new return the correct values
rspec ./spec/pam_client/dhx_spec.rb:6 # PamClient::Dhx validations is expected to belong to user required: false
rspec ./spec/pam_client/dhx_spec.rb:11 # PamClient::Dhx validations is expected to validate that :user cannot be empty/falsy
rspec ./spec/pam_client/grand_parent_agency_spec.rb:13 # generational methods a new object return the correct values
rspec ./spec/pam_client/grand_parent_agency_spec.rb:24 # generational methods with no children return the correct values
rspec ./spec/pam_client/grand_parent_agency_spec.rb:35 # generational methods with children return the correct values
rspec ./spec/pam_client/has_namespaced_name_spec.rb:35 # PamClient::Concerns::HasNamespacedName when included #as_json copies '[table_name]_name' to 'name'
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:3:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to a vp returns the ids and names of advertisers on which the user has registration assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:3:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:4:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to an ae returns the ids and names of advertisers on which the user has aor assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:4:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to an ae returns an empty array if the user has no aor assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:3:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "buying_ae" raises an InvalidOptionsError if no agency_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:3:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "buying_ae" raises an InvalidOptionsError if no advertiser_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:4:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "client_ae" raises an InvalidOptionsError if no agency_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:4:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "client_ae" raises an InvalidOptionsError if no advertiser_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:77 # PamClient::RegistrationDropdownValues#value_object type == agency raises an InvalidOptionsError if no property_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:88 # PamClient::RegistrationDropdownValues#value_object type == "advertiser" raises an InvalidOptionsError if no property_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:100 # PamClient::RegistrationDropdownValues#value_object type == "buying_ae" raises an InvalidOptionsError if no property_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:112 # PamClient::RegistrationDropdownValues#value_object type == "client_ae" raises an InvalidOptionsError if no property_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:124 # PamClient::RegistrationDropdownValues#value_object type == "property" raises an InvalidOptionsError if no user_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:131 # PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to a vp returns the ids and names of properties on which the user has registration assignments
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:138 # PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:146 # PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to an ae returns the ids and names of properties on which the user has aor assignments
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:155 # PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to an ae returns an empty array if the user has no registration or aor assignments
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:163 # PamClient::RegistrationDropdownValues#value_object type == "property" type == "buying_ae" returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:182 # PamClient::RegistrationDropdownValues#value_object type == "property" type == "client_ae" returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/pam_client/reports/has_additional_filters_spec.rb:13 # PamClient::Concerns::HasAdditionalFilters relevant_additional_filters returns only those filters that have values
rspec ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:16 # PamClient::Reports::MicrostrategyReportTag validations should validate duplicate records
rspec ./spec/pam_client/reports/saved_template_spec.rb:20 # PamClient::Reports::SavedTemplate#soft_destroy sets active to false
rspec ./spec/pam_client/reports/saved_template_spec.rb:25 # PamClient::Reports::SavedTemplate#soft_destroy destroys associated shared templates
rspec ./spec/pam_client/reports/saved_template_spec.rb:41 # PamClient::Reports::SavedTemplate#shareable_users returns active internal users not already having access to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:46 # PamClient::Reports::SavedTemplate#shareable_users does not return external users
rspec ./spec/pam_client/reports/saved_template_spec.rb:51 # PamClient::Reports::SavedTemplate#shareable_users does not return inactive users
rspec ./spec/pam_client/reports/saved_template_spec.rb:56 # PamClient::Reports::SavedTemplate#shareable_users does not return user associated to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:61 # PamClient::Reports::SavedTemplate#shareable_users deoes not return existing user that has share access to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:71 # PamClient::Reports::SavedTemplate.prompt_xml_for returns prompt_xml generated for the template
rspec ./spec/pam_client/reports/saved_template_spec.rb:75 # PamClient::Reports::SavedTemplate.prompt_xml_for contains the appended sso_id tag when provided
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:38 # PamClient::Reports::TrackPulledReport.recently_ran returns reports recently ran for a user
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:43 # PamClient::Reports::TrackPulledReport.recently_ran filters by dashboard
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:54 # PamClient::Reports::TrackPulledReport.recently_ran uniqueness returns unique reports
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:71 # PamClient::Reports::TrackPulledReport.recently_ran with saved templates returns unique report/template combinations
rspec ./spec/pam_client/reports/user_saved_templates_view_spec.rb:9 # PamClient::Reports::UserSavedTemplatesView.for_user returns only saved_templates for reports the user has access to
rspec ./spec/pam_client/shift_request_event_spec.rb:4 # PamClient::ShiftRequestEvent sorting shift request events should sort CREATED to the top and COMPLETED to the bottom
rspec './spec/pam_client/special_events_dropdown_values_spec.rb[1:1:1:1]' # PamClient::SpecialEventsDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if an invalid type is given
rspec './spec/pam_client/special_events_dropdown_values_spec.rb[1:1:1:2]' # PamClient::SpecialEventsDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if no type is given
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:52 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" raises an InvalidOptionsError if no user_id is given
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:63 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to a vp returns the ids and names of advertisers on which the user has registration assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:78 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:91 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to an ae returns the ids and names of advertisers on which the user has aor assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:102 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to an ae returns an empty array if the user has no aor assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:116 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" raises an InvalidOptionsError if no user_id is given
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:125 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" raises an InvalidOptionsError if no advertiser_id is given
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:136 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to a vp returns the ids and names of agencies on which the user has registration assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:148 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:161 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to an ae returns the ids and names of agencies on which the user has aor assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:172 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to an ae returns an empty array if the user has no aor assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:187 # PamClient::SpecialEventsDropdownValues.value_object type == "buying_ae" without selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:209 # PamClient::SpecialEventsDropdownValues.value_object type == "buying_ae" with selected_id returns the ids and names of actives aes, with the selected record being the supplied selected_id
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:232 # PamClient::SpecialEventsDropdownValues.value_object type == "client_ae" without selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:254 # PamClient::SpecialEventsDropdownValues.value_object type == "client_ae" with selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/rails8_compatibility_test_spec.rb:22 # Rails 8 Compatibility Tests Fixture Loading Behavior when fixtures are actually loaded should load fixtures successfully
rspec ./spec/rails8_compatibility_test_spec.rb:129 # Rails 8 Compatibility Tests Test Environment Setup Differences should show database cleaner configuration

