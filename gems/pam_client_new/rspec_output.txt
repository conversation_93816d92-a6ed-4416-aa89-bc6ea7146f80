Running only failed specs...
Run options: include {last_run_status: "failed"}

PamClient::AgencyDealCommentFactory
  create!
    when the agency has a parent agency & final spend of the amy record for that agency is true
DEBUG: agency4.id = 154673
DEBUG: parent_agency4.id = 154672
DEBUG: deal4.agency.id = 154673
DEBUG: Child AMY: agency_id=154673, final_spend=false
DEBUG: Parent AMY: agency_id=154672, final_spend=true
DEBUG: Factory agency_id = 
DEBUG: Factory final_spend? = false
DEBUG: Factory pre_or_post = pre
      creates a comment with nbcu_post_reg comment type depending on amy record of parent agency (FAILED - 1)

PamClient::AgencyStealthModePref
  relationships
    example at ./spec/pam_client/agency_stealth_mode_pref_spec.rb:5 (FAILED - 2)
    example at ./spec/pam_client/agency_stealth_mode_pref_spec.rb:7 (FAILED - 3)

PamClient::BaseAgency
  generational methods
    for an agency
      that is new
        return the correct values (FAILED - 4)

PamClient::Dhx
  validations
    is expected to belong to user required: false (FAILED - 5)
    is expected to validate that :user cannot be empty/falsy (FAILED - 6)

PamClient::ExternalDeal
  validations
    example at ./spec/pam_client/external_deal_spec.rb:6 (FAILED - 7)
    example at ./spec/pam_client/external_deal_spec.rb:8 (FAILED - 8)
    example at ./spec/pam_client/external_deal_spec.rb:10 (FAILED - 9)
    example at ./spec/pam_client/external_deal_spec.rb:12 (FAILED - 10)
    example at ./spec/pam_client/external_deal_spec.rb:14 (FAILED - 11)
    example at ./spec/pam_client/external_deal_spec.rb:16 (FAILED - 12)
    example at ./spec/pam_client/external_deal_spec.rb:18 (FAILED - 13)
    example at ./spec/pam_client/external_deal_spec.rb:20 (FAILED - 14)

generational methods
  a new object
    return the correct values (FAILED - 15)
  with no children
    return the correct values (FAILED - 16)
  with children
    return the correct values (FAILED - 17)

PamClient::Concerns::HasNamespacedName
  when included
    #as_json
      copies '[table_name]_name' to 'name' (FAILED - 18)

PamClient::PortalTerm
  validations
    example at ./spec/pam_client/portal_term_spec.rb:5 (FAILED - 19)
    example at ./spec/pam_client/portal_term_spec.rb:6 (FAILED - 20)
    example at ./spec/pam_client/portal_term_spec.rb:7 (FAILED - 21)
    example at ./spec/pam_client/portal_term_spec.rb:8 (FAILED - 22)
  .portal_term_name
    returns the task of portal term (FAILED - 23)
  .for_budgetyear_marketplace_and_portal_type
    only returns agency comments which are not submitted to AG (FAILED - 24)

PamClient::RegistrationDropdownValues
  .new
    behaves like a_new_dropdown_values_mixin_object
      raises an InvalidOptionsError if an invalid type is given (FAILED - 25)
      raises an InvalidOptionsError if no type is given (FAILED - 26)
  #value_object
    behaves like a_dropdown_values_mixin_value_object
      type == "agency"
        raises an InvalidOptionsError if no user_id is given (FAILED - 27)
        user_id belongs to a vp
          returns the ids and names of agencies on which the user has registration assignments (FAILED - 28)
          returns an empty array if the user has no registration assignments (FAILED - 29)
        user_id belongs to an ae
          returns the ids and names of agencies on which the user has aor assignments (FAILED - 30)
          returns an empty array if the user has no aor assignments (FAILED - 31)
      type == "advertiser"
        raises an InvalidOptionsError if no user_id is given (FAILED - 32)
        raises an InvalidOptionsError if no agency_id is given (FAILED - 33)
        user_id belongs to a vp
          returns the ids and names of advertisers on which the user has registration assignments (FAILED - 34)
          returns an empty array if the user has no registration assignments (FAILED - 35)
        user_id belongs to an ae
          returns the ids and names of advertisers on which the user has aor assignments (FAILED - 36)
          returns an empty array if the user has no aor assignments (FAILED - 37)
      type == "buying_ae"
        raises an InvalidOptionsError if no agency_id is given (FAILED - 38)
        raises an InvalidOptionsError if no advertiser_id is given (FAILED - 39)
      type == "client_ae"
        raises an InvalidOptionsError if no agency_id is given (FAILED - 40)
        raises an InvalidOptionsError if no advertiser_id is given (FAILED - 41)
    type == agency
      raises an InvalidOptionsError if no property_id is given (FAILED - 42)
    type == "advertiser"
      raises an InvalidOptionsError if no property_id is given (FAILED - 43)
    type == "buying_ae"
      raises an InvalidOptionsError if no property_id is given (FAILED - 44)
    type == "client_ae"
      raises an InvalidOptionsError if no property_id is given (FAILED - 45)
    type == "property"
      raises an InvalidOptionsError if no user_id is given (FAILED - 46)
      user_id belongs to a vp
        returns the ids and names of properties on which the user has registration assignments (FAILED - 47)
        returns an empty array if the user has no registration assignments (FAILED - 48)
      user_id belongs to an ae
        returns the ids and names of properties on which the user has aor assignments (FAILED - 49)
        returns an empty array if the user has no registration or aor assignments (FAILED - 50)
      type == "buying_ae"
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 51)
      type == "client_ae"
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 52)

PamClient::Concerns::HasAdditionalFilters
  relevant_additional_filters
    returns only those filters that have values (FAILED - 53)

PamClient::Reports::MicrostrategyReportTag
  validations
    should validate duplicate records (FAILED - 54)

PamClient::Reports::SavedTemplate
  #soft_destroy
    sets active to false (FAILED - 55)
    destroys associated shared templates (FAILED - 56)
  #shareable_users
    returns active internal users not already having access to template (FAILED - 57)
    does not return external users (FAILED - 58)
    does not return inactive users (FAILED - 59)
    does not return user associated to template (FAILED - 60)
    deoes not return existing user that has share access to template (FAILED - 61)
  .prompt_xml_for
    returns prompt_xml generated for the template (FAILED - 62)
    contains the appended sso_id tag when provided (FAILED - 63)

PamClient::Reports::TrackPulledReport
  .recently_ran
    returns reports recently ran for a user (FAILED - 64)
    filters by dashboard (FAILED - 65)
    uniqueness
      returns unique reports (FAILED - 66)
    with saved templates
      returns unique report/template combinations (FAILED - 67)

PamClient::Reports::UserSavedTemplatesView
  .for_user
    returns only saved_templates for reports the user has access to (FAILED - 68)

PamClient::ShiftRequestEvent
  sorting shift request events
    should sort CREATED to the top and COMPLETED to the bottom (FAILED - 69)

PamClient::SpecialEventsDropdownValues
  .new
    behaves like a_new_dropdown_values_mixin_object
      raises an InvalidOptionsError if an invalid type is given (FAILED - 70)
      raises an InvalidOptionsError if no type is given (FAILED - 71)
  .value_object
    type == "advertiser"
      raises an InvalidOptionsError if no user_id is given (FAILED - 72)
      user_id belongs to a vp
        returns the ids and names of advertisers on which the user has registration assignments (FAILED - 73)
        returns an empty array if the user has no registration assignments (FAILED - 74)
      user_id belongs to an ae
        returns the ids and names of advertisers on which the user has aor assignments (FAILED - 75)
        returns an empty array if the user has no aor assignments (FAILED - 76)
    type == "agency"
      raises an InvalidOptionsError if no user_id is given (FAILED - 77)
      raises an InvalidOptionsError if no advertiser_id is given (FAILED - 78)
      user_id belongs to a vp
        returns the ids and names of agencies on which the user has registration assignments (FAILED - 79)
        returns an empty array if the user has no registration assignments (FAILED - 80)
      user_id belongs to an ae
        returns the ids and names of agencies on which the user has aor assignments (FAILED - 81)
        returns an empty array if the user has no aor assignments (FAILED - 82)
    type == "buying_ae"
      without selected_id
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 83)
      with selected_id
        returns the ids and names of actives aes, with the selected record being the supplied selected_id (FAILED - 84)
    type == "client_ae"
      without selected_id
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 85)
      with selected_id
        returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params (FAILED - 86)

Rails 8 Compatibility Tests
  Fixture Loading Behavior
    when fixtures are actually loaded
      should load fixtures successfully (FAILED - 87)
  Test Environment Setup Differences
    should show database cleaner configuration (FAILED - 88)

Failures:

  1) PamClient::AgencyDealCommentFactory create! when the agency has a parent agency & final spend of the amy record for that agency is true creates a comment with nbcu_post_reg comment type depending on amy record of parent agency
     Failure/Error: expect(comments.first.comment_type).to eq(nbcu_post_registration)

       expected: #<PamClient::CommentType comment_type_id: 2942, comment_type_name: "nbcu_post_reg", created_at: "2025...0", updated_at: "2025-07-19 13:36:55.000000000 +0000", last_updated_by_id: -1, commentary_type: nil>
            got: #<PamClient::CommentType comment_type_id: 2941, comment_type_name: "nbcu_pre_reg", created_at: "2025-...0", updated_at: "2025-07-19 13:36:55.000000000 +0000", last_updated_by_id: -1, commentary_type: nil>

       (compared using ==)

       Diff:
       @@ -1,6 +1,6 @@
       -#<PamClient::CommentType:0x00000001391ff510
       - comment_type_id: 2942,
       - comment_type_name: "nbcu_post_reg",
       +#<PamClient::CommentType:0x0000000139f3d998
       + comment_type_id: 2941,
       + comment_type_name: "nbcu_pre_reg",
         created_at: "2025-07-19 13:36:55.000000000 +0000",
         updated_at: "2025-07-19 13:36:55.000000000 +0000",
         last_updated_by_id: -1,
     # ./spec/pam_client/agency_deal_comment_factory_spec.rb:157:in 'block (4 levels) in <module:PamClient>'

  2) PamClient::AgencyStealthModePref relationships 
     Failure/Error: it { is_expected.to belong_to(:base_agency) }

     ArgumentError:
       PamClient::AgencyStealthModePref model aliases `base_agency`, but `base_agency` is not an attribute. Use `alias_method :agency, :base_agency` or define the method manually.
     # ./spec/pam_client/agency_stealth_mode_pref_spec.rb:5:in 'block (3 levels) in <module:PamClient>'

  3) PamClient::AgencyStealthModePref relationships 
     Failure/Error: it { should validate_presence_of(:agency_id) }

     ArgumentError:
       PamClient::AgencyStealthModePref model aliases `base_agency`, but `base_agency` is not an attribute. Use `alias_method :agency, :base_agency` or define the method manually.
     # ./spec/pam_client/agency_stealth_mode_pref_spec.rb:7:in 'block (3 levels) in <module:PamClient>'

  4) PamClient::BaseAgency generational methods for an agency that is new return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/base_agency_spec.rb:106:in 'block (5 levels) in <module:PamClient>'

  5) PamClient::Dhx validations is expected to belong to user required: false
     Failure/Error: it { is_expected.to belong_to(:user) }
       Expected PamClient::Dhx to have a belongs_to association called user (PamClient::Dhx does not have a app_user_id foreign key.)
     # ./spec/pam_client/dhx_spec.rb:6:in 'block (3 levels) in <module:PamClient>'

  6) PamClient::Dhx validations is expected to validate that :user cannot be empty/falsy
     Failure/Error: it { should validate_presence_of(:user) }

     ActiveModel::MissingAttributeError:
       can't write unknown attribute `app_user_id`
     # ./spec/pam_client/dhx_spec.rb:11:in 'block (3 levels) in <module:PamClient>'

  7) PamClient::ExternalDeal validations 
     Failure/Error: it { is_expected.to have_valid(:property).when(stub_model(Property)) }

     NoMethodError:
       undefined method 'stub_model' for #<RSpec::ExampleGroups::PamClientExternalDeal::Validations:0x0000000148499500>
     # ./spec/pam_client/external_deal_spec.rb:6:in 'block (3 levels) in <module:PamClient>'

  8) PamClient::ExternalDeal validations 
     Failure/Error: it { is_expected.to have_valid(:marketplace).when(stub_model(Marketplace)) }

     NoMethodError:
       undefined method 'stub_model' for #<RSpec::ExampleGroups::PamClientExternalDeal::Validations:0x000000013fb33b80>
     # ./spec/pam_client/external_deal_spec.rb:8:in 'block (3 levels) in <module:PamClient>'

  9) PamClient::ExternalDeal validations 
     Failure/Error: it { is_expected.to have_valid(:status).when(stub_model(Status)) }

     NoMethodError:
       undefined method 'stub_model' for #<RSpec::ExampleGroups::PamClientExternalDeal::Validations:0x00000001392bee38>
     # ./spec/pam_client/external_deal_spec.rb:10:in 'block (3 levels) in <module:PamClient>'

  10) PamClient::ExternalDeal validations 
      Failure/Error: it { is_expected.to have_valid(:advertiser).when(stub_model(Advertiser)) }

      NoMethodError:
        undefined method 'stub_model' for #<RSpec::ExampleGroups::PamClientExternalDeal::Validations:0x000000014863f3c8>
      # ./spec/pam_client/external_deal_spec.rb:12:in 'block (3 levels) in <module:PamClient>'

  11) PamClient::ExternalDeal validations 
      Failure/Error: it { is_expected.to have_valid(:agency).when(stub_model(Agency)) }

      NoMethodError:
        undefined method 'stub_model' for #<RSpec::ExampleGroups::PamClientExternalDeal::Validations:0x000000013f8fbae8>
      # ./spec/pam_client/external_deal_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  12) PamClient::ExternalDeal validations 
      Failure/Error: it { is_expected.to have_valid(:demographic).when(stub_model(Demographic)) }

      NoMethodError:
        undefined method 'stub_model' for #<RSpec::ExampleGroups::PamClientExternalDeal::Validations:0x000000013aa5f150>
      # ./spec/pam_client/external_deal_spec.rb:16:in 'block (3 levels) in <module:PamClient>'

  13) PamClient::ExternalDeal validations 
      Failure/Error: it { is_expected.to have_valid(:budget_year).when(stub_model(BudgetYear)) }

      NoMethodError:
        undefined method 'stub_model' for #<RSpec::ExampleGroups::PamClientExternalDeal::Validations:0x000000013ffd0788>
      # ./spec/pam_client/external_deal_spec.rb:18:in 'block (3 levels) in <module:PamClient>'

  14) PamClient::ExternalDeal validations 
      Failure/Error: it { is_expected.to have_valid(:file_header).when(stub_model(FileHeader)) }

      NoMethodError:
        undefined method 'stub_model' for #<RSpec::ExampleGroups::PamClientExternalDeal::Validations:0x000000013c952a80>
      # ./spec/pam_client/external_deal_spec.rb:20:in 'block (3 levels) in <module:PamClient>'

  15) generational methods a new object return the correct values
      Failure/Error: expect(subject.parentless?).to be(true)

        expected true
             got false
      # ./spec/pam_client/grand_parent_agency_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  16) generational methods with no children return the correct values
      Failure/Error: expect(subject.parentless?).to be(true)

        expected true
             got false
      # ./spec/pam_client/grand_parent_agency_spec.rb:25:in 'block (3 levels) in <module:PamClient>'

  17) generational methods with children return the correct values
      Failure/Error: expect(subject.parentless?).to be(true)

        expected true
             got false
      # ./spec/pam_client/grand_parent_agency_spec.rb:36:in 'block (3 levels) in <module:PamClient>'

  18) PamClient::Concerns::HasNamespacedName when included #as_json copies '[table_name]_name' to 'name'
      Failure/Error: expect(record.as_json).to eq({ 'singular_name' => 'a value', 'name' => 'a value' })

        expected: {"name" => "a value", "singular_name" => "a value"}
             got: {"singular_name" => "a value"}

        (compared using ==)

        Diff:
        @@ -1,2 +1 @@
        -"name" => "a value",
         "singular_name" => "a value",
      # ./spec/pam_client/has_namespaced_name_spec.rb:37:in 'block (4 levels) in <module:PamClient>'

  19) PamClient::PortalTerm validations 
      Failure/Error: it { should validate_presence_of(:portal_checklist_task) }

      ArgumentError:
        PamClient::PortalTerm model aliases `portal_term_name`, but `portal_term_name` is not an attribute. Use `alias_method :name, :portal_term_name` or define the method manually.
      # ./spec/pam_client/portal_term_spec.rb:5:in 'block (3 levels) in <module:PamClient>'

  20) PamClient::PortalTerm validations 
      Failure/Error: it { should validate_presence_of(:budget_year_id) }

      ArgumentError:
        PamClient::PortalTerm model aliases `portal_term_name`, but `portal_term_name` is not an attribute. Use `alias_method :name, :portal_term_name` or define the method manually.
      # ./spec/pam_client/portal_term_spec.rb:6:in 'block (3 levels) in <module:PamClient>'

  21) PamClient::PortalTerm validations 
      Failure/Error: it { should validate_presence_of(:marketplace_id) }

      ArgumentError:
        PamClient::PortalTerm model aliases `portal_term_name`, but `portal_term_name` is not an attribute. Use `alias_method :name, :portal_term_name` or define the method manually.
      # ./spec/pam_client/portal_term_spec.rb:7:in 'block (3 levels) in <module:PamClient>'

  22) PamClient::PortalTerm validations 
      Failure/Error: it { should validate_presence_of(:display_order) }

      ArgumentError:
        PamClient::PortalTerm model aliases `portal_term_name`, but `portal_term_name` is not an attribute. Use `alias_method :name, :portal_term_name` or define the method manually.
      # ./spec/pam_client/portal_term_spec.rb:8:in 'block (3 levels) in <module:PamClient>'

  23) PamClient::PortalTerm.portal_term_name returns the task of portal term
      Failure/Error:
        portal_term = FactoryBot
                           .create(:portal_term,
                                   portal_checklist_task: 'Portal Term Task')

      ArgumentError:
        PamClient::PortalTerm model aliases `portal_term_name`, but `portal_term_name` is not an attribute. Use `alias_method :name, :portal_term_name` or define the method manually.
      # ./spec/pam_client/portal_term_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  24) PamClient::PortalTerm.for_budgetyear_marketplace_and_portal_type only returns agency comments which are not submitted to AG
      Failure/Error: create(:portal_term, budget_year: budget_year1, marketplace: marketplace, portal_term_type: portal_term_type, display_order: 1)

      ArgumentError:
        PamClient::PortalTerm model aliases `portal_term_name`, but `portal_term_name` is not an attribute. Use `alias_method :name, :portal_term_name` or define the method manually.
      # ./spec/pam_client/portal_term_spec.rb:26:in 'block (3 levels) in <module:PamClient>'

  25) PamClient::RegistrationDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if an invalid type is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_new_dropdown_values_mixin_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:68
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  26) PamClient::RegistrationDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if no type is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_new_dropdown_values_mixin_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:68
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  27) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" raises an InvalidOptionsError if no user_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  28) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" user_id belongs to a vp returns the ids and names of agencies on which the user has registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 28, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 28, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  29) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" user_id belongs to a vp returns an empty array if the user has no registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  30) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" user_id belongs to an ae returns the ids and names of agencies on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  31) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" user_id belongs to an ae returns an empty array if the user has no aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  32) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" raises an InvalidOptionsError if no user_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 11, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 11, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  33) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" raises an InvalidOptionsError if no agency_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 27, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 27, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  34) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to a vp returns the ids and names of advertisers on which the user has registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 13, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 13, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  35) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to a vp returns an empty array if the user has no registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 30, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 30, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  36) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to an ae returns the ids and names of advertisers on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  37) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to an ae returns an empty array if the user has no aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  38) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "buying_ae" raises an InvalidOptionsError if no agency_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 13, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 13, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  39) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "buying_ae" raises an InvalidOptionsError if no advertiser_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 33, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 33, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  40) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "client_ae" raises an InvalidOptionsError if no agency_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 30, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 30, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  41) PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "client_ae" raises an InvalidOptionsError if no advertiser_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 14, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_dropdown_values_mixin_value_object" called from ./spec/pam_client/registration_dropdown_values_spec.rb:74
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 14, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  42) PamClient::RegistrationDropdownValues#value_object type == agency raises an InvalidOptionsError if no property_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 25, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 25, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  43) PamClient::RegistrationDropdownValues#value_object type == "advertiser" raises an InvalidOptionsError if no property_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 11, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 11, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  44) PamClient::RegistrationDropdownValues#value_object type == "buying_ae" raises an InvalidOptionsError if no property_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  45) PamClient::RegistrationDropdownValues#value_object type == "client_ae" raises an InvalidOptionsError if no property_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 27, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 27, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  46) PamClient::RegistrationDropdownValues#value_object type == "property" raises an InvalidOptionsError if no user_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  47) PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to a vp returns the ids and names of properties on which the user has registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 26, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 26, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  48) PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to a vp returns an empty array if the user has no registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 22, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 22, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  49) PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to an ae returns the ids and names of properties on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 28, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 28, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  50) PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to an ae returns an empty array if the user has no registration or aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 19, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 19, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  51) PamClient::RegistrationDropdownValues#value_object type == "property" type == "buying_ae" returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  52) PamClient::RegistrationDropdownValues#value_object type == "property" type == "client_ae" returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 28, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 28, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  53) PamClient::Concerns::HasAdditionalFilters relevant_additional_filters returns only those filters that have values
      Failure/Error: let(:form) {Reports::MicrostrategyForm.new}

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/has_additional_filters_spec.rb:7:in 'block (3 levels) in <module:PamClient>'
      # ./spec/pam_client/reports/has_additional_filters_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  54) PamClient::Reports::MicrostrategyReportTag validations should validate duplicate records
      Failure/Error: mstr_report_tag_2 = MicrostrategyReportTag.new(tag: mstr_report_tag_1.tag, report: mstr_report_tag_1.report)

      ActiveModel::UnknownAttributeError:
        unknown attribute 'tag' for PamClient::Reports::MicrostrategyReportTag.
      # ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:18:in 'block (3 levels) in <module:Reports>'
      # ------------------
      # --- Caused by: ---
      # NoMethodError:
      #   undefined method 'tag=' for an instance of PamClient::Reports::MicrostrategyReportTag
      #   ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  55) PamClient::Reports::SavedTemplate#soft_destroy sets active to false
      Failure/Error: let(:saved_template_with_share) { create(:saved_template) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:17:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  56) PamClient::Reports::SavedTemplate#soft_destroy destroys associated shared templates
      Failure/Error: let(:saved_template_with_share) { create(:saved_template) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:17:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  57) PamClient::Reports::SavedTemplate#shareable_users returns active internal users not already having access to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  58) PamClient::Reports::SavedTemplate#shareable_users does not return external users
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  59) PamClient::Reports::SavedTemplate#shareable_users does not return inactive users
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  60) PamClient::Reports::SavedTemplate#shareable_users does not return user associated to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  61) PamClient::Reports::SavedTemplate#shareable_users deoes not return existing user that has share access to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  62) PamClient::Reports::SavedTemplate.prompt_xml_for returns prompt_xml generated for the template
      Failure/Error: let!(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:69:in 'block (3 levels) in <module:Reports>'

  63) PamClient::Reports::SavedTemplate.prompt_xml_for contains the appended sso_id tag when provided
      Failure/Error: let!(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:69:in 'block (3 levels) in <module:Reports>'

  64) PamClient::Reports::TrackPulledReport.recently_ran returns reports recently ran for a user
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  65) PamClient::Reports::TrackPulledReport.recently_ran filters by dashboard
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  66) PamClient::Reports::TrackPulledReport.recently_ran uniqueness returns unique reports
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  67) PamClient::Reports::TrackPulledReport.recently_ran with saved templates returns unique report/template combinations
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  68) PamClient::Reports::UserSavedTemplatesView.for_user returns only saved_templates for reports the user has access to
      Failure/Error: mstr_form = create(:microstrategy_form, microstrategy_report: report)

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/user_saved_templates_view_spec.rb:14:in 'block (3 levels) in <module:Reports>'

  69) PamClient::ShiftRequestEvent sorting shift request events should sort CREATED to the top and COMPLETED to the bottom
      Failure/Error: expect(ary).to eq(expected)

        expected: [#<PamClient::ShiftRequestEvent shift_request_event_id: 365, shift_request_event_name: "Shift Creatio...:37:17.000000000 +0000", updated_at: "2025-07-19 13:37:17.000000000 +0000", last_updated_by_id: -1>]
             got: [#<PamClient::ShiftRequestEvent shift_request_event_id: 366, shift_request_event_name: "Email Sent", ...:37:17.000000000 +0000", updated_at: "2025-07-19 13:37:17.000000000 +0000", last_updated_by_id: -1>]

        (compared using ==)

        Diff:
        @@ -1,3 +1,3 @@
        -[#<PamClient::ShiftRequestEvent shift_request_event_id: 365, shift_request_event_name: "Shift Creation", previous_event: nil, created_at: "2025-07-19 13:37:17.000000000 +0000", updated_at: "2025-07-19 13:37:17.000000000 +0000", last_updated_by_id: -1>,
        - #<PamClient::ShiftRequestEvent shift_request_event_id: 366, shift_request_event_name: "Email Sent", previous_event: nil, created_at: "2025-07-19 13:37:17.000000000 +0000", updated_at: "2025-07-19 13:37:17.000000000 +0000", last_updated_by_id: -1>,
        +[#<PamClient::ShiftRequestEvent shift_request_event_id: 366, shift_request_event_name: "Email Sent", previous_event: nil, created_at: "2025-07-19 13:37:17.000000000 +0000", updated_at: "2025-07-19 13:37:17.000000000 +0000", last_updated_by_id: -1>,
        + #<PamClient::ShiftRequestEvent shift_request_event_id: 365, shift_request_event_name: "Shift Creation", previous_event: nil, created_at: "2025-07-19 13:37:17.000000000 +0000", updated_at: "2025-07-19 13:37:17.000000000 +0000", last_updated_by_id: -1>,
          #<PamClient::ShiftRequestEvent shift_request_event_id: 364, shift_request_event_name: "Shift Completed", previous_event: nil, created_at: "2025-07-19 13:37:17.000000000 +0000", updated_at: "2025-07-19 13:37:17.000000000 +0000", last_updated_by_id: -1>]
      # ./spec/pam_client/shift_request_event_spec.rb:16:in 'block (3 levels) in <module:PamClient>'

  70) PamClient::SpecialEventsDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if an invalid type is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 12, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_new_dropdown_values_mixin_object" called from ./spec/pam_client/special_events_dropdown_values_spec.rb:45
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 12, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  71) PamClient::SpecialEventsDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if no type is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      Shared Example Group: "a_new_dropdown_values_mixin_object" called from ./spec/pam_client/special_events_dropdown_values_spec.rb:45
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  72) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" raises an InvalidOptionsError if no user_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 11, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 11, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  73) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to a vp returns the ids and names of advertisers on which the user has registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 32, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 32, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  74) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to a vp returns an empty array if the user has no registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  75) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to an ae returns the ids and names of advertisers on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  76) PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to an ae returns an empty array if the user has no aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  77) PamClient::SpecialEventsDropdownValues.value_object type == "agency" raises an InvalidOptionsError if no user_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 17, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  78) PamClient::SpecialEventsDropdownValues.value_object type == "agency" raises an InvalidOptionsError if no advertiser_id is given
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 11, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 11, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  79) PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to a vp returns the ids and names of agencies on which the user has registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 15, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 15, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  80) PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to a vp returns an empty array if the user has no registration assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 15, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 15, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  81) PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to an ae returns the ids and names of agencies on which the user has aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 30, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 30, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  82) PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to an ae returns an empty array if the user has no aor assignments
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 18, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  83) PamClient::SpecialEventsDropdownValues.value_object type == "buying_ae" without selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 16, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  84) PamClient::SpecialEventsDropdownValues.value_object type == "buying_ae" with selected_id returns the ids and names of actives aes, with the selected record being the supplied selected_id
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 14, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 14, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  85) PamClient::SpecialEventsDropdownValues.value_object type == "client_ae" without selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 19, maximum: 2)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_CODE" (actual: 19, maximum: 2)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  86) PamClient::SpecialEventsDropdownValues.value_object type == "client_ae" with selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
      Failure/Error: let!(:gs1) { create(:geo_state, location: loc1) }

      ActiveRecord::ValueTooLong:
        OCIError: ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 32, maximum: 25)
        Help: https://docs.oracle.com/error-help/db/ora-12899/
      # stmt.c:267:in oci8lib_340.bundle
      # ./spec/support/shared_contexts/dropdown_values_mixin_context.rb:6:in 'block (2 levels) in <top (required)>'
      # ------------------
      # --- Caused by: ---
      # OCIError:
      #   ORA-12899: value too large for column "SMS_TEST"."GEO_STATE"."GEO_STATE_NAME" (actual: 32, maximum: 25)
      #   Help: https://docs.oracle.com/error-help/db/ora-12899/
      #   stmt.c:267:in oci8lib_340.bundle

  87) Rails 8 Compatibility Tests Fixture Loading Behavior when fixtures are actually loaded should load fixtures successfully
      Failure/Error: expect(sponsorship_types(:sponsorship_type_001)).to be_present

      NoMethodError:
        undefined method 'sponsorship_types' for #<RSpec::ExampleGroups::Rails8CompatibilityTests::FixtureLoadingBehavior::WhenFixturesAreActuallyLoaded:0x00000001490b1298>
      # ./spec/rails8_compatibility_test_spec.rb:23:in 'block (4 levels) in <top (required)>'

  88) Rails 8 Compatibility Tests Test Environment Setup Differences should show database cleaner configuration
      Failure/Error: puts "DatabaseCleaner strategy: #{DatabaseCleaner.strategy}"

      NoMethodError:
        undefined method 'strategy' for module DatabaseCleaner
      # ./spec/rails8_compatibility_test_spec.rb:131:in 'block (3 levels) in <top (required)>'

Finished in 29.01 seconds (files took 7.33 seconds to load)
88 examples, 88 failures

Failed examples:

rspec ./spec/pam_client/agency_deal_comment_factory_spec.rb:140 # PamClient::AgencyDealCommentFactory create! when the agency has a parent agency & final spend of the amy record for that agency is true creates a comment with nbcu_post_reg comment type depending on amy record of parent agency
rspec ./spec/pam_client/agency_stealth_mode_pref_spec.rb:5 # PamClient::AgencyStealthModePref relationships 
rspec ./spec/pam_client/agency_stealth_mode_pref_spec.rb:7 # PamClient::AgencyStealthModePref relationships 
rspec ./spec/pam_client/base_agency_spec.rb:105 # PamClient::BaseAgency generational methods for an agency that is new return the correct values
rspec ./spec/pam_client/dhx_spec.rb:6 # PamClient::Dhx validations is expected to belong to user required: false
rspec ./spec/pam_client/dhx_spec.rb:11 # PamClient::Dhx validations is expected to validate that :user cannot be empty/falsy
rspec ./spec/pam_client/external_deal_spec.rb:6 # PamClient::ExternalDeal validations 
rspec ./spec/pam_client/external_deal_spec.rb:8 # PamClient::ExternalDeal validations 
rspec ./spec/pam_client/external_deal_spec.rb:10 # PamClient::ExternalDeal validations 
rspec ./spec/pam_client/external_deal_spec.rb:12 # PamClient::ExternalDeal validations 
rspec ./spec/pam_client/external_deal_spec.rb:14 # PamClient::ExternalDeal validations 
rspec ./spec/pam_client/external_deal_spec.rb:16 # PamClient::ExternalDeal validations 
rspec ./spec/pam_client/external_deal_spec.rb:18 # PamClient::ExternalDeal validations 
rspec ./spec/pam_client/external_deal_spec.rb:20 # PamClient::ExternalDeal validations 
rspec ./spec/pam_client/grand_parent_agency_spec.rb:13 # generational methods a new object return the correct values
rspec ./spec/pam_client/grand_parent_agency_spec.rb:24 # generational methods with no children return the correct values
rspec ./spec/pam_client/grand_parent_agency_spec.rb:35 # generational methods with children return the correct values
rspec ./spec/pam_client/has_namespaced_name_spec.rb:35 # PamClient::Concerns::HasNamespacedName when included #as_json copies '[table_name]_name' to 'name'
rspec ./spec/pam_client/portal_term_spec.rb:5 # PamClient::PortalTerm validations 
rspec ./spec/pam_client/portal_term_spec.rb:6 # PamClient::PortalTerm validations 
rspec ./spec/pam_client/portal_term_spec.rb:7 # PamClient::PortalTerm validations 
rspec ./spec/pam_client/portal_term_spec.rb:8 # PamClient::PortalTerm validations 
rspec ./spec/pam_client/portal_term_spec.rb:12 # PamClient::PortalTerm.portal_term_name returns the task of portal term
rspec ./spec/pam_client/portal_term_spec.rb:31 # PamClient::PortalTerm.for_budgetyear_marketplace_and_portal_type only returns agency comments which are not submitted to AG
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:1:1:1]' # PamClient::RegistrationDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if an invalid type is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:1:1:2]' # PamClient::RegistrationDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if no type is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:1:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" raises an InvalidOptionsError if no user_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:1:2:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" user_id belongs to a vp returns the ids and names of agencies on which the user has registration assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:1:2:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:1:3:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" user_id belongs to an ae returns the ids and names of agencies on which the user has aor assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:1:3:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "agency" user_id belongs to an ae returns an empty array if the user has no aor assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" raises an InvalidOptionsError if no user_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" raises an InvalidOptionsError if no agency_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:3:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to a vp returns the ids and names of advertisers on which the user has registration assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:3:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:4:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to an ae returns the ids and names of advertisers on which the user has aor assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:2:4:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "advertiser" user_id belongs to an ae returns an empty array if the user has no aor assignments
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:3:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "buying_ae" raises an InvalidOptionsError if no agency_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:3:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "buying_ae" raises an InvalidOptionsError if no advertiser_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:4:1]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "client_ae" raises an InvalidOptionsError if no agency_id is given
rspec './spec/pam_client/registration_dropdown_values_spec.rb[1:2:1:4:2]' # PamClient::RegistrationDropdownValues#value_object behaves like a_dropdown_values_mixin_value_object type == "client_ae" raises an InvalidOptionsError if no advertiser_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:77 # PamClient::RegistrationDropdownValues#value_object type == agency raises an InvalidOptionsError if no property_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:88 # PamClient::RegistrationDropdownValues#value_object type == "advertiser" raises an InvalidOptionsError if no property_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:100 # PamClient::RegistrationDropdownValues#value_object type == "buying_ae" raises an InvalidOptionsError if no property_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:112 # PamClient::RegistrationDropdownValues#value_object type == "client_ae" raises an InvalidOptionsError if no property_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:124 # PamClient::RegistrationDropdownValues#value_object type == "property" raises an InvalidOptionsError if no user_id is given
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:131 # PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to a vp returns the ids and names of properties on which the user has registration assignments
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:138 # PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:146 # PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to an ae returns the ids and names of properties on which the user has aor assignments
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:155 # PamClient::RegistrationDropdownValues#value_object type == "property" user_id belongs to an ae returns an empty array if the user has no registration or aor assignments
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:163 # PamClient::RegistrationDropdownValues#value_object type == "property" type == "buying_ae" returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/pam_client/registration_dropdown_values_spec.rb:182 # PamClient::RegistrationDropdownValues#value_object type == "property" type == "client_ae" returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/pam_client/reports/has_additional_filters_spec.rb:13 # PamClient::Concerns::HasAdditionalFilters relevant_additional_filters returns only those filters that have values
rspec ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:16 # PamClient::Reports::MicrostrategyReportTag validations should validate duplicate records
rspec ./spec/pam_client/reports/saved_template_spec.rb:20 # PamClient::Reports::SavedTemplate#soft_destroy sets active to false
rspec ./spec/pam_client/reports/saved_template_spec.rb:25 # PamClient::Reports::SavedTemplate#soft_destroy destroys associated shared templates
rspec ./spec/pam_client/reports/saved_template_spec.rb:41 # PamClient::Reports::SavedTemplate#shareable_users returns active internal users not already having access to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:46 # PamClient::Reports::SavedTemplate#shareable_users does not return external users
rspec ./spec/pam_client/reports/saved_template_spec.rb:51 # PamClient::Reports::SavedTemplate#shareable_users does not return inactive users
rspec ./spec/pam_client/reports/saved_template_spec.rb:56 # PamClient::Reports::SavedTemplate#shareable_users does not return user associated to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:61 # PamClient::Reports::SavedTemplate#shareable_users deoes not return existing user that has share access to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:71 # PamClient::Reports::SavedTemplate.prompt_xml_for returns prompt_xml generated for the template
rspec ./spec/pam_client/reports/saved_template_spec.rb:75 # PamClient::Reports::SavedTemplate.prompt_xml_for contains the appended sso_id tag when provided
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:38 # PamClient::Reports::TrackPulledReport.recently_ran returns reports recently ran for a user
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:43 # PamClient::Reports::TrackPulledReport.recently_ran filters by dashboard
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:54 # PamClient::Reports::TrackPulledReport.recently_ran uniqueness returns unique reports
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:71 # PamClient::Reports::TrackPulledReport.recently_ran with saved templates returns unique report/template combinations
rspec ./spec/pam_client/reports/user_saved_templates_view_spec.rb:9 # PamClient::Reports::UserSavedTemplatesView.for_user returns only saved_templates for reports the user has access to
rspec ./spec/pam_client/shift_request_event_spec.rb:4 # PamClient::ShiftRequestEvent sorting shift request events should sort CREATED to the top and COMPLETED to the bottom
rspec './spec/pam_client/special_events_dropdown_values_spec.rb[1:1:1:1]' # PamClient::SpecialEventsDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if an invalid type is given
rspec './spec/pam_client/special_events_dropdown_values_spec.rb[1:1:1:2]' # PamClient::SpecialEventsDropdownValues.new behaves like a_new_dropdown_values_mixin_object raises an InvalidOptionsError if no type is given
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:52 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" raises an InvalidOptionsError if no user_id is given
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:63 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to a vp returns the ids and names of advertisers on which the user has registration assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:78 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:91 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to an ae returns the ids and names of advertisers on which the user has aor assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:102 # PamClient::SpecialEventsDropdownValues.value_object type == "advertiser" user_id belongs to an ae returns an empty array if the user has no aor assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:116 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" raises an InvalidOptionsError if no user_id is given
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:125 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" raises an InvalidOptionsError if no advertiser_id is given
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:136 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to a vp returns the ids and names of agencies on which the user has registration assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:148 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to a vp returns an empty array if the user has no registration assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:161 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to an ae returns the ids and names of agencies on which the user has aor assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:172 # PamClient::SpecialEventsDropdownValues.value_object type == "agency" user_id belongs to an ae returns an empty array if the user has no aor assignments
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:187 # PamClient::SpecialEventsDropdownValues.value_object type == "buying_ae" without selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:209 # PamClient::SpecialEventsDropdownValues.value_object type == "buying_ae" with selected_id returns the ids and names of actives aes, with the selected record being the supplied selected_id
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:232 # PamClient::SpecialEventsDropdownValues.value_object type == "client_ae" without selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/pam_client/special_events_dropdown_values_spec.rb:254 # PamClient::SpecialEventsDropdownValues.value_object type == "client_ae" with selected_id returns the ids and names of active aes, with the selected record being the ae with an aor assignment for the given params
rspec ./spec/rails8_compatibility_test_spec.rb:22 # Rails 8 Compatibility Tests Fixture Loading Behavior when fixtures are actually loaded should load fixtures successfully
rspec ./spec/rails8_compatibility_test_spec.rb:129 # Rails 8 Compatibility Tests Test Environment Setup Differences should show database cleaner configuration

