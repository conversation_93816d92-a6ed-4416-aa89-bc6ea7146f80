Running only failed specs...
Run options: include {last_run_status: "failed"}

PamClient::BaseAgency
  generational methods
    for an agency
      that is new
        return the correct values (FAILED - 1)

PamClient::Dhx
  validations
    is expected to belong to user required: false (FAILED - 2)
    is expected to validate that :user cannot be empty/falsy (FAILED - 3)

generational methods
  a new object
    return the correct values (FAILED - 4)
  with no children
    return the correct values (FAILED - 5)
  with children
    return the correct values (FAILED - 6)

PamClient::Concerns::HasAdditionalFilters
  relevant_additional_filters
    returns only those filters that have values (FAILED - 7)

PamClient::Reports::MicrostrategyReportTag
  validations
    should validate duplicate records (FAILED - 8)

PamClient::Reports::SavedTemplate
  #soft_destroy
    sets active to false (FAILED - 9)
    destroys associated shared templates (FAILED - 10)
  #shareable_users
    returns active internal users not already having access to template (FAILED - 11)
    does not return external users (FAILED - 12)
    does not return inactive users (FAILED - 13)
    does not return user associated to template (FAILED - 14)
    deoes not return existing user that has share access to template (FAILED - 15)
  .prompt_xml_for
    returns prompt_xml generated for the template (FAILED - 16)
    contains the appended sso_id tag when provided (FAILED - 17)

PamClient::Reports::TrackPulledReport
  .recently_ran
    returns reports recently ran for a user (FAILED - 18)
    filters by dashboard (FAILED - 19)
    uniqueness
      returns unique reports (FAILED - 20)
    with saved templates
      returns unique report/template combinations (FAILED - 21)

PamClient::Reports::UserSavedTemplatesView
  .for_user
    returns only saved_templates for reports the user has access to (FAILED - 22)

PamClient::ShiftRequestEvent
  sorting shift request events
    should sort CREATED to the top and COMPLETED to the bottom (FAILED - 23)

Rails 8 Compatibility Tests
  Fixture Loading Behavior
    when fixtures are actually loaded
      should load fixtures successfully (FAILED - 24)
  Test Environment Setup Differences
    should show database cleaner configuration (FAILED - 25)

Failures:

  1) PamClient::BaseAgency generational methods for an agency that is new return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/base_agency_spec.rb:106:in 'block (5 levels) in <module:PamClient>'

  2) PamClient::Dhx validations is expected to belong to user required: false
     Failure/Error: it { is_expected.to belong_to(:user) }
       Expected PamClient::Dhx to have a belongs_to association called user (PamClient::Dhx does not have a app_user_id foreign key.)
     # ./spec/pam_client/dhx_spec.rb:6:in 'block (3 levels) in <module:PamClient>'

  3) PamClient::Dhx validations is expected to validate that :user cannot be empty/falsy
     Failure/Error: it { should validate_presence_of(:user) }

     ActiveModel::MissingAttributeError:
       can't write unknown attribute `app_user_id`
     # ./spec/pam_client/dhx_spec.rb:11:in 'block (3 levels) in <module:PamClient>'

  4) generational methods a new object return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  5) generational methods with no children return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:25:in 'block (3 levels) in <module:PamClient>'

  6) generational methods with children return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:36:in 'block (3 levels) in <module:PamClient>'

  7) PamClient::Concerns::HasAdditionalFilters relevant_additional_filters returns only those filters that have values
     Failure/Error: let(:form) {Reports::MicrostrategyForm.new}

     ArgumentError:
       PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
     # ./spec/pam_client/reports/has_additional_filters_spec.rb:7:in 'block (3 levels) in <module:PamClient>'
     # ./spec/pam_client/reports/has_additional_filters_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  8) PamClient::Reports::MicrostrategyReportTag validations should validate duplicate records
     Failure/Error: mstr_report_tag_2 = MicrostrategyReportTag.new(tag: mstr_report_tag_1.tag, report: mstr_report_tag_1.report)

     ActiveModel::UnknownAttributeError:
       unknown attribute 'tag' for PamClient::Reports::MicrostrategyReportTag.
     # ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:18:in 'block (3 levels) in <module:Reports>'
     # ------------------
     # --- Caused by: ---
     # NoMethodError:
     #   undefined method 'tag=' for an instance of PamClient::Reports::MicrostrategyReportTag
     #   ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  9) PamClient::Reports::SavedTemplate#soft_destroy sets active to false
     Failure/Error: let(:saved_template_with_share) { create(:saved_template) }

     ArgumentError:
       PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
     # ./spec/pam_client/reports/saved_template_spec.rb:17:in 'block (3 levels) in <module:Reports>'
     # ./spec/pam_client/reports/saved_template_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  10) PamClient::Reports::SavedTemplate#soft_destroy destroys associated shared templates
      Failure/Error: let(:saved_template_with_share) { create(:saved_template) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:17:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  11) PamClient::Reports::SavedTemplate#shareable_users returns active internal users not already having access to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  12) PamClient::Reports::SavedTemplate#shareable_users does not return external users
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  13) PamClient::Reports::SavedTemplate#shareable_users does not return inactive users
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  14) PamClient::Reports::SavedTemplate#shareable_users does not return user associated to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  15) PamClient::Reports::SavedTemplate#shareable_users deoes not return existing user that has share access to template
      Failure/Error: let(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:38:in 'block (3 levels) in <module:Reports>'
      # ./spec/pam_client/reports/saved_template_spec.rb:39:in 'block (3 levels) in <module:Reports>'

  16) PamClient::Reports::SavedTemplate.prompt_xml_for returns prompt_xml generated for the template
      Failure/Error: let!(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:69:in 'block (3 levels) in <module:Reports>'

  17) PamClient::Reports::SavedTemplate.prompt_xml_for contains the appended sso_id tag when provided
      Failure/Error: let!(:saved_template) { create(:saved_template, user: template_user) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/saved_template_spec.rb:69:in 'block (3 levels) in <module:Reports>'

  18) PamClient::Reports::TrackPulledReport.recently_ran returns reports recently ran for a user
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  19) PamClient::Reports::TrackPulledReport.recently_ran filters by dashboard
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  20) PamClient::Reports::TrackPulledReport.recently_ran uniqueness returns unique reports
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  21) PamClient::Reports::TrackPulledReport.recently_ran with saved templates returns unique report/template combinations
      Failure/Error: let!(:tpr_u1) { create(:track_pulled_report, user: user1, microstrategy_report: mstr_report.first) }

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/track_pulled_report_spec.rb:34:in 'block (3 levels) in <module:Reports>'

  22) PamClient::Reports::UserSavedTemplatesView.for_user returns only saved_templates for reports the user has access to
      Failure/Error: mstr_form = create(:microstrategy_form, microstrategy_report: report)

      ArgumentError:
        PamClient::Reports::MicrostrategyForm model aliases `microstrategy_form_name`, but `microstrategy_form_name` is not an attribute. Use `alias_method :name, :microstrategy_form_name` or define the method manually.
      # ./spec/pam_client/reports/user_saved_templates_view_spec.rb:14:in 'block (3 levels) in <module:Reports>'

  23) PamClient::ShiftRequestEvent sorting shift request events should sort CREATED to the top and COMPLETED to the bottom
      Failure/Error: expect(ary).to eq(expected)

        expected: [#<PamClient::ShiftRequestEvent shift_request_event_id: 377, shift_request_event_name: "Shift Creatio...:24:31.000000000 +0000", updated_at: "2025-07-19 14:24:31.000000000 +0000", last_updated_by_id: -1>]
             got: [#<PamClient::ShiftRequestEvent shift_request_event_id: 378, shift_request_event_name: "Email Sent", ...:24:31.000000000 +0000", updated_at: "2025-07-19 14:24:31.000000000 +0000", last_updated_by_id: -1>]

        (compared using ==)

        Diff:
        @@ -1,3 +1,3 @@
        -[#<PamClient::ShiftRequestEvent shift_request_event_id: 377, shift_request_event_name: "Shift Creation", previous_event: nil, created_at: "2025-07-19 14:24:31.000000000 +0000", updated_at: "2025-07-19 14:24:31.000000000 +0000", last_updated_by_id: -1>,
        - #<PamClient::ShiftRequestEvent shift_request_event_id: 378, shift_request_event_name: "Email Sent", previous_event: nil, created_at: "2025-07-19 14:24:31.000000000 +0000", updated_at: "2025-07-19 14:24:31.000000000 +0000", last_updated_by_id: -1>,
        +[#<PamClient::ShiftRequestEvent shift_request_event_id: 378, shift_request_event_name: "Email Sent", previous_event: nil, created_at: "2025-07-19 14:24:31.000000000 +0000", updated_at: "2025-07-19 14:24:31.000000000 +0000", last_updated_by_id: -1>,
        + #<PamClient::ShiftRequestEvent shift_request_event_id: 377, shift_request_event_name: "Shift Creation", previous_event: nil, created_at: "2025-07-19 14:24:31.000000000 +0000", updated_at: "2025-07-19 14:24:31.000000000 +0000", last_updated_by_id: -1>,
          #<PamClient::ShiftRequestEvent shift_request_event_id: 376, shift_request_event_name: "Shift Completed", previous_event: nil, created_at: "2025-07-19 14:24:31.000000000 +0000", updated_at: "2025-07-19 14:24:31.000000000 +0000", last_updated_by_id: -1>]
      # ./spec/pam_client/shift_request_event_spec.rb:16:in 'block (3 levels) in <module:PamClient>'

  24) Rails 8 Compatibility Tests Fixture Loading Behavior when fixtures are actually loaded should load fixtures successfully
      Failure/Error: expect(sponsorship_types(:sponsorship_type_001)).to be_present

      NoMethodError:
        undefined method 'sponsorship_types' for #<RSpec::ExampleGroups::Rails8CompatibilityTests::FixtureLoadingBehavior::WhenFixturesAreActuallyLoaded:0x00000001259f7b28>
      # ./spec/rails8_compatibility_test_spec.rb:23:in 'block (4 levels) in <top (required)>'

  25) Rails 8 Compatibility Tests Test Environment Setup Differences should show database cleaner configuration
      Failure/Error: puts "DatabaseCleaner strategy: #{DatabaseCleaner.strategy}"

      NoMethodError:
        undefined method 'strategy' for module DatabaseCleaner
      # ./spec/rails8_compatibility_test_spec.rb:131:in 'block (3 levels) in <top (required)>'

Finished in 13.94 seconds (files took 9.41 seconds to load)
25 examples, 25 failures

Failed examples:

rspec ./spec/pam_client/base_agency_spec.rb:105 # PamClient::BaseAgency generational methods for an agency that is new return the correct values
rspec ./spec/pam_client/dhx_spec.rb:6 # PamClient::Dhx validations is expected to belong to user required: false
rspec ./spec/pam_client/dhx_spec.rb:11 # PamClient::Dhx validations is expected to validate that :user cannot be empty/falsy
rspec ./spec/pam_client/grand_parent_agency_spec.rb:13 # generational methods a new object return the correct values
rspec ./spec/pam_client/grand_parent_agency_spec.rb:24 # generational methods with no children return the correct values
rspec ./spec/pam_client/grand_parent_agency_spec.rb:35 # generational methods with children return the correct values
rspec ./spec/pam_client/reports/has_additional_filters_spec.rb:13 # PamClient::Concerns::HasAdditionalFilters relevant_additional_filters returns only those filters that have values
rspec ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:16 # PamClient::Reports::MicrostrategyReportTag validations should validate duplicate records
rspec ./spec/pam_client/reports/saved_template_spec.rb:20 # PamClient::Reports::SavedTemplate#soft_destroy sets active to false
rspec ./spec/pam_client/reports/saved_template_spec.rb:25 # PamClient::Reports::SavedTemplate#soft_destroy destroys associated shared templates
rspec ./spec/pam_client/reports/saved_template_spec.rb:41 # PamClient::Reports::SavedTemplate#shareable_users returns active internal users not already having access to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:46 # PamClient::Reports::SavedTemplate#shareable_users does not return external users
rspec ./spec/pam_client/reports/saved_template_spec.rb:51 # PamClient::Reports::SavedTemplate#shareable_users does not return inactive users
rspec ./spec/pam_client/reports/saved_template_spec.rb:56 # PamClient::Reports::SavedTemplate#shareable_users does not return user associated to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:61 # PamClient::Reports::SavedTemplate#shareable_users deoes not return existing user that has share access to template
rspec ./spec/pam_client/reports/saved_template_spec.rb:71 # PamClient::Reports::SavedTemplate.prompt_xml_for returns prompt_xml generated for the template
rspec ./spec/pam_client/reports/saved_template_spec.rb:75 # PamClient::Reports::SavedTemplate.prompt_xml_for contains the appended sso_id tag when provided
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:38 # PamClient::Reports::TrackPulledReport.recently_ran returns reports recently ran for a user
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:43 # PamClient::Reports::TrackPulledReport.recently_ran filters by dashboard
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:54 # PamClient::Reports::TrackPulledReport.recently_ran uniqueness returns unique reports
rspec ./spec/pam_client/reports/track_pulled_report_spec.rb:71 # PamClient::Reports::TrackPulledReport.recently_ran with saved templates returns unique report/template combinations
rspec ./spec/pam_client/reports/user_saved_templates_view_spec.rb:9 # PamClient::Reports::UserSavedTemplatesView.for_user returns only saved_templates for reports the user has access to
rspec ./spec/pam_client/shift_request_event_spec.rb:4 # PamClient::ShiftRequestEvent sorting shift request events should sort CREATED to the top and COMPLETED to the bottom
rspec ./spec/rails8_compatibility_test_spec.rb:22 # Rails 8 Compatibility Tests Fixture Loading Behavior when fixtures are actually loaded should load fixtures successfully
rspec ./spec/rails8_compatibility_test_spec.rb:129 # Rails 8 Compatibility Tests Test Environment Setup Differences should show database cleaner configuration

