Running only failed specs...

An error occurred while loading spec_helper.
Failure/Error: views = ActiveRecord::Base.connection.views + ActiveRecord::Base.connection.materialized_views + [:ar_internal_metadata]

OCIError:
  ORA-12541: Cannot connect. No listener at host 127.0.0.1 port 1521.
  Help: https://docs.oracle.com/error-help/db/ora-12541/
# oci8.c:561:in oci8lib_340.bundle
# ./spec/support/database_cleaner.rb:1:in '<top (required)>'
# ./spec/spec_helper.rb:33:in 'block in <top (required)>'
# ./spec/spec_helper.rb:33:in 'Array#each'
# ./spec/spec_helper.rb:33:in '<top (required)>'

To use `--only-failures`, you must first set `config.example_status_persistence_file_path`.

To use `--only-failures`, you must first set `config.example_status_persistence_file_path`.


