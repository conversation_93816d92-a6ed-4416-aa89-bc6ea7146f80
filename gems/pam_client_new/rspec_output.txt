Running only failed specs...
Run options: include {last_run_status: "failed"}

PamClient::BaseAgency
  generational methods
    for an agency
      that is new
        return the correct values (FAILED - 1)

PamClient::Dhx
  validations
    is expected to belong to user required: false (FAILED - 2)
    is expected to validate that :user cannot be empty/falsy (FAILED - 3)

generational methods
  a new object
    return the correct values (FAILED - 4)
  with no children
    return the correct values (FAILED - 5)
  with children
    return the correct values (FAILED - 6)

PamClient::Reports::MicrostrategyReportTag
  validations
    should validate duplicate records (FAILED - 7)

PamClient::Reports::SavedTemplate
  #soft_destroy
    sets active to false
    destroys associated shared templates
  #shareable_users
    returns active internal users not already having access to template
    does not return external users
    does not return inactive users
    does not return user associated to template
    deoes not return existing user that has share access to template
  .prompt_xml_for
    returns prompt_xml generated for the template
    contains the appended sso_id tag when provided

PamClient::Reports::TrackPulledReport
  .recently_ran
    returns reports recently ran for a user
    filters by dashboard
    uniqueness
      returns unique reports
    with saved templates
      returns unique report/template combinations

PamClient::Reports::UserSavedTemplatesView
  .for_user
    returns only saved_templates for reports the user has access to

PamClient::ShiftRequestEvent
  sorting shift request events
    should sort CREATED to the top and COMPLETED to the bottom (FAILED - 8)

Rails 8 Compatibility Tests
  Fixture Loading Behavior
    when fixtures are actually loaded
      should load fixtures successfully (FAILED - 9)
  Test Environment Setup Differences
    should show database cleaner configuration (FAILED - 10)

Failures:

  1) PamClient::BaseAgency generational methods for an agency that is new return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/base_agency_spec.rb:106:in 'block (5 levels) in <module:PamClient>'

  2) PamClient::Dhx validations is expected to belong to user required: false
     Failure/Error: it { is_expected.to belong_to(:user) }
       Expected PamClient::Dhx to have a belongs_to association called user (PamClient::Dhx does not have a app_user_id foreign key.)
     # ./spec/pam_client/dhx_spec.rb:6:in 'block (3 levels) in <module:PamClient>'

  3) PamClient::Dhx validations is expected to validate that :user cannot be empty/falsy
     Failure/Error: it { should validate_presence_of(:user) }

     ActiveModel::MissingAttributeError:
       can't write unknown attribute `app_user_id`
     # ./spec/pam_client/dhx_spec.rb:11:in 'block (3 levels) in <module:PamClient>'

  4) generational methods a new object return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:14:in 'block (3 levels) in <module:PamClient>'

  5) generational methods with no children return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:25:in 'block (3 levels) in <module:PamClient>'

  6) generational methods with children return the correct values
     Failure/Error: expect(subject.parentless?).to be(true)

       expected true
            got false
     # ./spec/pam_client/grand_parent_agency_spec.rb:36:in 'block (3 levels) in <module:PamClient>'

  7) PamClient::Reports::MicrostrategyReportTag validations should validate duplicate records
     Failure/Error: mstr_report_tag_2 = MicrostrategyReportTag.new(tag: mstr_report_tag_1.tag, report: mstr_report_tag_1.report)

     ActiveModel::UnknownAttributeError:
       unknown attribute 'tag' for PamClient::Reports::MicrostrategyReportTag.
     # ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:18:in 'block (3 levels) in <module:Reports>'
     # ------------------
     # --- Caused by: ---
     # NoMethodError:
     #   undefined method 'tag=' for an instance of PamClient::Reports::MicrostrategyReportTag
     #   ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:18:in 'block (3 levels) in <module:Reports>'

  8) PamClient::ShiftRequestEvent sorting shift request events should sort CREATED to the top and COMPLETED to the bottom
     Failure/Error: expect(ary).to eq(expected)

       expected: [#<PamClient::ShiftRequestEvent shift_request_event_id: 380, shift_request_event_name: "Shift Creatio...:14:28.000000000 +0000", updated_at: "2025-07-19 15:14:28.000000000 +0000", last_updated_by_id: -1>]
            got: [#<PamClient::ShiftRequestEvent shift_request_event_id: 381, shift_request_event_name: "Email Sent", ...:14:28.000000000 +0000", updated_at: "2025-07-19 15:14:28.000000000 +0000", last_updated_by_id: -1>]

       (compared using ==)

       Diff:
       @@ -1,3 +1,3 @@
       -[#<PamClient::ShiftRequestEvent shift_request_event_id: 380, shift_request_event_name: "Shift Creation", previous_event: nil, created_at: "2025-07-19 15:14:28.000000000 +0000", updated_at: "2025-07-19 15:14:28.000000000 +0000", last_updated_by_id: -1>,
       - #<PamClient::ShiftRequestEvent shift_request_event_id: 381, shift_request_event_name: "Email Sent", previous_event: nil, created_at: "2025-07-19 15:14:28.000000000 +0000", updated_at: "2025-07-19 15:14:28.000000000 +0000", last_updated_by_id: -1>,
       +[#<PamClient::ShiftRequestEvent shift_request_event_id: 381, shift_request_event_name: "Email Sent", previous_event: nil, created_at: "2025-07-19 15:14:28.000000000 +0000", updated_at: "2025-07-19 15:14:28.000000000 +0000", last_updated_by_id: -1>,
       + #<PamClient::ShiftRequestEvent shift_request_event_id: 380, shift_request_event_name: "Shift Creation", previous_event: nil, created_at: "2025-07-19 15:14:28.000000000 +0000", updated_at: "2025-07-19 15:14:28.000000000 +0000", last_updated_by_id: -1>,
         #<PamClient::ShiftRequestEvent shift_request_event_id: 379, shift_request_event_name: "Shift Completed", previous_event: nil, created_at: "2025-07-19 15:14:28.000000000 +0000", updated_at: "2025-07-19 15:14:28.000000000 +0000", last_updated_by_id: -1>]
     # ./spec/pam_client/shift_request_event_spec.rb:16:in 'block (3 levels) in <module:PamClient>'

  9) Rails 8 Compatibility Tests Fixture Loading Behavior when fixtures are actually loaded should load fixtures successfully
     Failure/Error: expect(sponsorship_types(:sponsorship_type_001)).to be_present

     NoMethodError:
       undefined method 'sponsorship_types' for #<RSpec::ExampleGroups::Rails8CompatibilityTests::FixtureLoadingBehavior::WhenFixturesAreActuallyLoaded:0x00000001438234f0>
     # ./spec/rails8_compatibility_test_spec.rb:23:in 'block (4 levels) in <top (required)>'

  10) Rails 8 Compatibility Tests Test Environment Setup Differences should show database cleaner configuration
      Failure/Error: puts "DatabaseCleaner strategy: #{DatabaseCleaner.strategy}"

      NoMethodError:
        undefined method 'strategy' for module DatabaseCleaner
      # ./spec/rails8_compatibility_test_spec.rb:131:in 'block (3 levels) in <top (required)>'

Finished in 16.88 seconds (files took 6.97 seconds to load)
24 examples, 10 failures

Failed examples:

rspec ./spec/pam_client/base_agency_spec.rb:105 # PamClient::BaseAgency generational methods for an agency that is new return the correct values
rspec ./spec/pam_client/dhx_spec.rb:6 # PamClient::Dhx validations is expected to belong to user required: false
rspec ./spec/pam_client/dhx_spec.rb:11 # PamClient::Dhx validations is expected to validate that :user cannot be empty/falsy
rspec ./spec/pam_client/grand_parent_agency_spec.rb:13 # generational methods a new object return the correct values
rspec ./spec/pam_client/grand_parent_agency_spec.rb:24 # generational methods with no children return the correct values
rspec ./spec/pam_client/grand_parent_agency_spec.rb:35 # generational methods with children return the correct values
rspec ./spec/pam_client/reports/microstrategy_report_tag_spec.rb:16 # PamClient::Reports::MicrostrategyReportTag validations should validate duplicate records
rspec ./spec/pam_client/shift_request_event_spec.rb:4 # PamClient::ShiftRequestEvent sorting shift request events should sort CREATED to the top and COMPLETED to the bottom
rspec ./spec/rails8_compatibility_test_spec.rb:22 # Rails 8 Compatibility Tests Fixture Loading Behavior when fixtures are actually loaded should load fixtures successfully
rspec ./spec/rails8_compatibility_test_spec.rb:129 # Rails 8 Compatibility Tests Test Environment Setup Differences should show database cleaner configuration

