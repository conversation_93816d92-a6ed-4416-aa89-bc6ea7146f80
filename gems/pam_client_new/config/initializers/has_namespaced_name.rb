# frozen_string_literal: true

# DISABLED: This initializer was meant for pam-api application, not for gem testing
# When testing the gem itself, we want to keep the original as_json behavior
#
# if defined?(PamClient::Concerns::HasNamespacedName)
#   module PamClient
#     module Concerns
#       module HasNamespacedName
#         # as_json behavior is modified in PamClient for reasons
#         # that don't apply to pam_api therefore undo-ing it here
#         # https://github.com/CINBCUniversal/pam_client/blob/develop/lib/pam_client/has_namespaced_name.rb#L9
#         if method_defined?(:as_json)
#           remove_method(:as_json)
#         end
#       end
#     end
#   end
# end
