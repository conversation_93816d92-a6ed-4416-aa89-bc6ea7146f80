# frozen_string_literal: true

require 'simplecov' if ENV['COVERAGE']

# Set Rails environment for testing
ENV['RAILS_ENV'] ||= 'test'

# Load Rails environment - this will load the full Rails 8 application
require File.expand_path('../config/environment', __dir__)

# Prevent database truncation if the environment is production
abort('The Rails environment is running in production mode!') if Rails.env.production?

require 'rspec/rails'
require 'active_record/fixtures'
require 'rspec/rails/fixture_support'
require 'webmock/rspec'
require 'ffaker'
require 'stringio'

# Configure WebMock
WebMock.disable_net_connect!(allow_localhost: true)

# Stub AWS EC2 metadata service to prevent WebMock errors
WebMock.stub_request(:put, "http://***************/latest/api/token")
  .to_return(status: 200, body: "", headers: {})
WebMock.stub_request(:get, "http://***************/latest/meta-data/iam/security-credentials/")
  .to_return(status: 404, body: "", headers: {})
WebMock.stub_request(:get, %r{http://169\.254\.169\.254/latest/meta-data/.*})
  .to_return(status: 404, body: "", headers: {})

# Load support files
Dir[Rails.root.join('spec/support/**/*.rb')].each { |f| require f }

# Load PamClient factories safely (suppress verbose output)
begin
  if defined?(PamClient) && PamClient.respond_to?(:root)
    # Load all factory files from PamClient gem
    factory_files = Dir[File.join(PamClient.root, 'spec/factories/**/*.rb')].sort

    # Suppress output during factory loading
    original_stdout = $stdout
    original_stderr = $stderr
    $stdout = StringIO.new
    $stderr = StringIO.new

    factory_files.each do |f|
      load f
    end
  end
rescue StandardError => e
  Rails.logger.warn("Could not load PamClient factories: #{e.message}")
ensure
  # Restore output streams
  $stdout = original_stdout if original_stdout
  $stderr = original_stderr if original_stderr
end

require 'database_cleaner'
# require 'valid_attribute' # Temporarily disabled - gem not available
require 'pry'
require 'nbc_conventions' # Required for constraint disabling functionality
require 'pam_client/support'

RSpec.configure do |config|
  config.include(FactoryBot::Syntax::Methods)
  config.include ActiveSupport::Testing::TimeHelpers
  config.include RSpec::Rails::FixtureSupport

  # Enable --only-failures feature to rerun only failed specs
  config.example_status_persistence_file_path = "spec/examples.txt"

  # Configure fixture support
  config.fixture_paths = [File.join(File.dirname(__FILE__), "fixtures")]
  config.use_transactional_fixtures = false

  # Provide fake AWS credentials to prevent credential lookups
  Aws.config.update(
    access_key_id: 'fake_access_key',
    secret_access_key: 'fake_secret_key',
    region: 'us-east-1'
  )

  config.before(:each) do |example|
    # Create unmapped values for each test
    PamClient::Support::UnmappedValues.create_all

    # Stub AWS EC2 metadata service to prevent credential lookups
    stub_request(:any, /169\.254\.169\.254/).to_return(status: 404)
  end

  config.before :suite do
    begin
      # Test database connection before proceeding - more robust check
      if ActiveRecord::Base.connected? && ActiveRecord::Base.connection.active?
        # Only test Oracle-specific query if we have Oracle adapter
        if ActiveRecord::Base.connection.adapter_name == 'OracleEnhanced'
          ActiveRecord::Base.connection.execute('SELECT 1 FROM DUAL')
        else
          # For other adapters, just test basic connection
          ActiveRecord::Base.connection.execute('SELECT 1')
        end

        # Use deletion strategy like original pam_client for better test isolation
        views = ActiveRecord::Base.connection.views + ActiveRecord::Base.connection.materialized_views + [:ar_internal_metadata]
        DatabaseCleaner[:active_record].strategy = :deletion, { except: views }
        DatabaseCleaner[:active_record].clean_with(:deletion, except: views)

        # Only seed essential -1 records, not full application seeds
        # This matches the original pam_client approach
        begin
          if defined?(PamClient::Support::UnmappedValues)
            PamClient::Support::UnmappedValues.create_all
          end
        rescue StandardError => e
          Rails.logger.warn("Unmapped values seeding failed: #{e.message}")
        end

        if ENV['DB_CONSTRAINTS'] == 'true'
          NbcConventions::Constraints.enable
        else
          NbcConventions::Constraints.disable
        end
        Rails.logger.info("Database setup completed successfully")
      end
    rescue ActiveRecord::ConnectionNotEstablished, ActiveRecord::NoDatabaseError, StandardError => e
      Rails.logger.warn("Database setup failed: #{e.message}")
      Rails.logger.warn("Skipping database setup - database not available")
    end
  end
end

# Configure RSpec for database cleaning and test setup
RSpec.configure do |config|
  # Database cleaning configuration (matches original pam_client)
  config.before :all do
    # Check database availability within the block
    begin
      ActiveRecord::Base.connection.execute('SELECT 1 FROM DUAL')
      DatabaseCleaner[:active_record].start
      DatabaseCleaner[:active_record].clean
    rescue StandardError => e
      Rails.logger.warn("Database not available in before :all: #{e.message}")
    end
  end

  config.before :each do
    # Check database availability and create essential records
    begin
      ActiveRecord::Base.connection.execute('SELECT 1 FROM DUAL')
      DatabaseCleaner[:active_record].start
      # Create essential -1 records before each test (matches original pam_client)
      PamClient::Support::UnmappedValues.create_all if defined?(PamClient::Support::UnmappedValues)
    rescue StandardError => e
      Rails.logger.warn("Database setup failed in before :each: #{e.message}")
    end
  end

  config.after :each do
    # Clean database after each test
    begin
      DatabaseCleaner[:active_record].clean
    rescue StandardError => e
      Rails.logger.warn("Database cleanup failed in after :each: #{e.message}")
    end
  end

  # Include test helpers
  config.include FactoryBot::Syntax::Methods if defined?(FactoryBot)
  config.include ActiveSupport::Testing::TimeHelpers if defined?(ActiveSupport::Testing::TimeHelpers)
end

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :active_record
    with.library :active_model
    with.library :rails
  end
end

# These variables are set at app level;
# Declaring dummy values here for the sake of specs
ENV['SFDC_PITCH_URL'] = 'donut'
ENV['SFDC_PITCH_VIEW'] = 'waffle'