FactoryBot.define do
  factory :parent_agency, class: PamClient::ParentAgency do
    sequence(:agency_name) { |n| "#{FFaker::Company.name} #{n}" }
    parent_agency_id { -1 }  # Explicit value instead of relying on database trigger
  end
end


FactoryBot.define do
  factory :base_agency, class: PamClient::BaseAgency do
    sequence(:agency_name) { |n| "#{FFaker::Company.name} #{n}" }
    parent_agency_id { -1 }  # Explicit value instead of relying on database trigger
  end
end
