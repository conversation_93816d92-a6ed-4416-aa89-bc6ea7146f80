FactoryBot.define do
  factory :deal, class: PamClient::Deal do
    parent_deal
    agency
    advertiser
    property
    demographic
    account_executive
    client_ae { nil }
    planning_ae { nil }
    category
    rating_stream
    marketplace { PamClient::Marketplace.current_default || build(:marketplace) }
    deal_tag_id { -1 }    # Explicit value instead of relying on database trigger
    vertical_id { -1 }   # Explicit value instead of relying on database trigger
    multi_year { false }
    sponsorship_type
    sales_type_id { -1 }
    content_id { -1 }
    partnership_id { -1 }
    advertiser_brand_id { -1 }
    sfdc_intg_vertical_id { -1 }
  end
end
