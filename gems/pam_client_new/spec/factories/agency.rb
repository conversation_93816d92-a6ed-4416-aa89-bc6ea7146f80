FactoryBot.define do
  factory :agency, class: PamClient::Agency do
    transient do
      user { true }
    end

    sequence(:agency_name) { |n| "#{FFaker::Company.name} #{n}" }
    parent_agency

    trait :without_parent do
      parent_agency { nil }
    end

    trait :with_children do
      transient do
        num_children { 2 }
      end
      after(:create) do |agency, evaluator|
        create_list(:agency, evaluator.num_children, parent_agency_id: agency.id)
      end
    end

    trait :with_assignment do
      after(:create) do |agency, evaluator|
        FactoryBot.create(:assignment, user: evaluator.user, agency: agency)
      end
    end
  end

  factory :grandparent_agency, class: PamClient::GrandParentAgency do
    sequence(:agency_name) { |n| "#{FFaker::Company.name} #{n}" }
    parent_agency_id { -1 }  # Grandparent agencies should be parentless

    trait :with_children do
      after(:create) do |gp, _evaluator|
        gp.parent_agencies = create_list(:parent_agency, 2)
      end
    end
  end
end
