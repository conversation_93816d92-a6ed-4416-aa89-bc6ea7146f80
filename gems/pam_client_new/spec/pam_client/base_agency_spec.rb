# frozen_string_literal: true

require 'spec_helper'

module PamClient
  describe BaseAgency, type: :model do
    it_should_behave_like 'a model with the has namespaced name mixin'

    let(:parent_agency) { FactoryBot.create(:parent_agency, name: 'Parent Agency') }
    let(:parentless_agency) do
      FactoryBot.create(:agency, name: 'Parentless Agency', parent_agency_id: -1)
    end
    let(:child_agency) do
      FactoryBot.create(:agency, name: 'child Agency', parent_agency_id: parent_agency.id)
    end
    let(:parent_agency_hash) do
      { type: 'parent-agency', agency_id: parent_agency.id,
        name: parent_agency.name, ask: false, current_projection: false,
        registration: false, children: [] }
    end

    describe 'validations' do
      it { should validate_presence_of(:agency_name) }
      xit { should validate_uniqueness_of(:agency_name) }

      it { should validate_presence_of(:agency_type) }

      it { is_expected.to have_valid(:email).when(nil) }
      it { is_expected.to have_valid(:email).when('<EMAIL>') }
      it { is_expected.not_to have_valid(:email).when('foo@bar') }
    end

    context 'agency tree' do
      let!(:agency) { create(:agency, parent_agency_id: -1) }

      let!(:parent_agency) { create(:parent_agency, parent_agency_id: -1) }
      let!(:agency_with_parent) { create(:agency, parent_agency_id: parent_agency.id) }

      let!(:grandparent_agency) { create(:grandparent_agency, parent_agency_id: -1) }
      let!(:parent_agency_with_grandparent) { create(:parent_agency, parent_agency_id: grandparent_agency.id) }
      let!(:agency_with_parent_and_grandparent) {
        create(:agency, parent_agency_id: parent_agency_with_grandparent.id)
      }

      describe '.top_level_agencies' do
        it 'returns only agencies without parents' do
          top_level_agency_ids = BaseAgency.top_level_agencies.pluck(:agency_id)
          expect(top_level_agency_ids).to include(agency.id, parent_agency.id, grandparent_agency.id)
          expect(top_level_agency_ids).not_to include(
            agency_with_parent.id,
            parent_agency_with_grandparent.id,
            agency_with_parent_and_grandparent.id
          )
        end
      end
    end

    describe 'callback #format_type' do
      it 'correctly formats attribute when sent namespaced agnecy_type' do
        agency = FactoryBot.create(:base_agency, agency_type: 'PamClient::Agency')
        parent_agency = FactoryBot.create(:base_agency, agency_type: 'PamClient::ParentAgency')
        grand_parent_agency =
          FactoryBot.create(:base_agency, agency_type: 'PamClient::GrandParentAgency')

        expect(agency.agency_type).to eq 'Agency'
        expect(parent_agency.agency_type).to eq 'ParentAgency'
        expect(grand_parent_agency.agency_type).to eq 'GrandParentAgency'
      end
    end

    describe '.parent_or_orphan_agency' do
      subject { BaseAgency }

      it 'should return nil for non-existant agency' do
        expect(subject.parent_or_orphan_agency(666)).to be_nil
      end

      describe 'for a child agency' do
        let(:child) { create(:agency) }
        it 'should return nil' do
          expect(subject.parent_or_orphan_agency(child.id)).to be_nil
        end
      end

      describe 'for a parent agency' do
        let(:parent) { create(:agency, :with_children) }
        it 'should return the parent' do
          expect(subject.parent_or_orphan_agency(parent.id)).to eq(parent)
        end
      end

      describe 'for an orphan agency' do
        let(:orphan) { create(:agency, :without_parent) }
        it 'should return the orphan' do
          expect(subject.parent_or_orphan_agency(orphan.id)).to eq(orphan)
        end
      end
    end

    describe 'generational methods' do
      describe 'for an agency' do
        describe 'that is new' do
          let(:subject) { build(:agency, :without_parent) }

          it 'return the correct values' do
            expect(subject.parentless?).to be(true)
            expect(subject.children).to be_empty
            expect(subject.orphan?).to be(true)
            expect(subject.parent?).to be(false)
          end
        end

        describe 'with no children' do
          let(:subject) { create(:agency, :without_parent) }

          it 'return the correct values' do
            expect(subject.parentless?).to be(true)
            expect(subject.children).to be_empty
            expect(subject.orphan?).to be(true)
            expect(subject.parent?).to be(false)
          end
        end

        describe 'with children' do
          let(:subject) { create(:agency, :without_parent, :with_children) }

          it 'return the correct values' do
            expect(subject.parentless?).to be(true)
            expect(subject.children).to_not be_empty
            expect(subject.orphan?).to be(false)
            expect(subject.parent?).to be(true)
          end
        end
      end
    end

    # @deprecated
    describe '#is_parent_agency?' do
      it 'returns true when agency is parent agency' do
        expect(parent_agency.is_parent_agency?).to eq true
      end

      it 'returns false when agency is parent-less agency' do
        expect(parentless_agency.is_parent_agency?).to eq false
      end
    end

    describe '#agency_detail' do
      it 'returns the hash of agency detail' do
        expect(parent_agency.agency_detail).to eq parent_agency_hash
      end
    end

    describe '#collect_child_agencies' do
      it 'returns the hash for child agencies of parent agency' do
        hsh = [{ type: 'child-agency', agency_id: child_agency.id,
                 name: child_agency.name, ask: false, current_projection: false,
                 registration: false, children: [] }]

        expect(parent_agency.collect_child_agencies(parent_agency_hash)).to eq hsh
      end
    end

    describe '#agencies_from_top_level' do
      it 'returns all the active children and grandchildren of a given grandparent agency' do
        grand_parent_agency = create(:grandparent_agency)
        parent_agency = create(:parent_agency, grand_parent_agency: grand_parent_agency)
        inactive_parent = create(:parent_agency, grand_parent_agency: grand_parent_agency, active: false)
        agency = create(:agency, parent_agency: parent_agency)
        agency2 = create(:agency, parent_agency: inactive_parent)
        
        expect(BaseAgency.by_top_level_id(grand_parent_agency.id)).to eq([agency])
      end

      it 'returns all the children and active grandchildren of a given grandparent agency' do
        grand_parent_agency = create(:grandparent_agency)
        parent_agency = create(:parent_agency, grand_parent_agency: grand_parent_agency)
        agency = create(:agency, parent_agency: parent_agency)
        inactive_agency = create(:agency, parent_agency: parent_agency, active: false)
        
        expect(BaseAgency.by_top_level_id(grand_parent_agency.id)).to eq([agency])
      end

      it 'returns all the active agencies of a given parent agency' do
        parent_agency = create(:parent_agency)
        agency = create(:agency, parent_agency: parent_agency)
        inactive_agency = create(:agency, parent_agency: parent_agency, active: false)
        
        expect(BaseAgency.by_top_level_id(parent_agency.id)).to eq([agency])
      end

      it 'returns the given agency if it is not a gpa/parent' do
        agency = create(:agency)
        expect(BaseAgency.by_top_level_id(agency.id)).to eq([agency])
      end
    end
  end
end
