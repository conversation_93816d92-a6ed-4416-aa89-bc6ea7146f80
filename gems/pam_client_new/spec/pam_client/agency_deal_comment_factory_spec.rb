# frozen_string_literal: true

require 'spec_helper'

module PamClient
  describe AgencyDealCommentFactory do
    let!(:user) { create(:user) }

    describe 'initialize' do
      let!(:budget) { create(:budget) }
      let!(:budget_year) { budget.budget_year }
      let!(:deal) { budget.deal }
      let!(:message) { 'Hi, there!' }
      let!(:expected) do
        [budget.id, budget_year.id, deal.id, message, user.id, user.full_name]
      end

      it 'populates the instance vars' do
        adcf = AgencyDealCommentFactory.new(budget, user, message)
        result = [
          adcf.budget.id,
          adcf.budget_year.id,
          adcf.deal.id,
          adcf.message,
          adcf.user.id,
          adcf.user.full_name
        ]
        expect(result).to match_array(expected)
      end
    end

    describe 'create!' do
      let!(:message) { "Howdy, ya'll!" }
      let!(:marketplace) { create(:marketplace) }
      let!(:nbcu_pre_registration) { CommentType.nbcu_pre_registration }
      let!(:nbcu_post_registration) { CommentType.nbcu_post_registration }
      let!(:comments) { AgencyDealComment.all }

      before :each do
        nbcu_pre_registration
        nbcu_post_registration
      end

      context 'when the AgencyDealCommentFactory is called' do
        let!(:budget) { create(:budget) }

        it 'creates a new AgencyDealComment' do
          old_count = AgencyDealComment.all.count
          adcf = AgencyDealCommentFactory.new(budget, user, message)
          adcf.create!
          new_count = AgencyDealComment.all.count
          expect(new_count).to eq(old_count + 1)
        end
      end

      context 'when the agency is a parentless agency & final spend is true' do
        let!(:agency1) { create(:agency, :without_parent) }
        let!(:deal1) { create(:deal, agency: agency1, marketplace: marketplace) }
        let!(:budget1) { create(:budget, deal: deal1) }
        let!(:budget_year1) { budget1.budget_year }
        let!(:agency_marketplace_year1) {
          create(:agency_marketplace_year, agency: agency1,
                                           budget_year: budget_year1,
                                           marketplace: marketplace,
                                           final_spend: true)
        }

        it 'creates a comment with nbcu_post_reg comment type ' do
          agency_marketplace_year1
          AgencyDealCommentFactory.new(budget1, user, message).create!
          expect(comments.first.comment_type).to eq(nbcu_post_registration)
        end
      end

      context 'when the agency is a parentless agency & final spend is false' do
        let!(:agency2) { create(:agency, :without_parent) }
        let!(:deal2) { create(:deal, agency: agency2, marketplace: marketplace) }
        let!(:budget2) { create(:budget, deal: deal2) }
        let!(:budget_year2) { budget2.budget_year }
        let!(:agency_marketplace_year2) {
          create(:agency_marketplace_year, agency: agency2,
                                           budget_year: budget_year2,
                                           marketplace: marketplace,
                                           final_spend: false)
        }

        it 'creates a comment with nbcu_pre_reg comment type ' do
          agency_marketplace_year2
          AgencyDealCommentFactory.new(budget2, user, message).create!
          expect(comments.first.comment_type).to eq(nbcu_pre_registration)
        end
      end

      context 'when the agency has a parent agency & final spend of the amy record for that agency is false' do
        let!(:agency3) { create(:agency) }
        let!(:parent_agency3) { BaseAgency.find(agency3.parent_agency_id) }
        let!(:deal3) { create(:deal, agency: agency3, marketplace: marketplace) }
        let!(:budget3) { create(:budget, deal: deal3) }
        let!(:budget_year3) { budget3.budget_year }
        let!(:agency_marketplace_year3i) {
          create(:agency_marketplace_year, agency: agency3,
                                           budget_year: budget_year3,
                                           marketplace: marketplace,
                                           final_spend: true)
        }
        let!(:agency_marketplace_year3ii) {
          create(:agency_marketplace_year, agency: parent_agency3,
                                           budget_year: budget_year3,
                                           marketplace: marketplace,
                                           final_spend: false)
        }

        it 'creates a comment with nbcu_pre_reg comment type depending on amy record of parent agency' do
          agency_marketplace_year3i
          agency_marketplace_year3ii
          AgencyDealCommentFactory.new(budget3, user, message).create!
          expect(comments.first.comment_type).to eq(nbcu_pre_registration)
        end
      end

      context 'when the agency has a parent agency & final spend of the amy record for that agency is true' do
        let!(:agency4) { create(:agency) }
        let!(:parent_agency4) { BaseAgency.find(agency4.parent_agency_id) }
        let!(:deal4) { create(:deal, agency: agency4, marketplace: marketplace) }
        let!(:budget4) { create(:budget, deal: deal4) }
        let!(:budget_year4) { budget4.budget_year }
        let!(:agency_marketplace_year4i) {
          create(:agency_marketplace_year, agency: agency4,
                                           budget_year: budget_year4,
                                           marketplace: marketplace,
                                           final_spend: false)
        }
        let!(:agency_marketplace_year4ii) {
          create(:agency_marketplace_year, agency: parent_agency4,
                                           budget_year: budget_year4,
                                           marketplace: marketplace,
                                           final_spend: true)
        }

        it 'creates a comment with nbcu_post_reg comment type depending on amy record of parent agency' do
          agency_marketplace_year4i
          agency_marketplace_year4ii

          # Debug output
          puts "DEBUG: agency4.id = #{agency4.id}"
          puts "DEBUG: parent_agency4.id = #{parent_agency4.id}"
          puts "DEBUG: deal4.agency.id = #{deal4.agency.id}"
          puts "DEBUG: Child AMY: agency_id=#{agency_marketplace_year4i.agency_id}, final_spend=#{agency_marketplace_year4i.final_spend}"
          puts "DEBUG: Parent AMY: agency_id=#{agency_marketplace_year4ii.agency_id}, final_spend=#{agency_marketplace_year4ii.final_spend}"

          factory = AgencyDealCommentFactory.new(budget4, user, message)
          ordered_agency = factory.send(:ordered_agency)
          puts "DEBUG: OrderedAgency found: #{ordered_agency.inspect}"
          if ordered_agency
            puts "DEBUG: OrderedAgency.agency_id = #{ordered_agency.agency_id}"
            puts "DEBUG: OrderedAgency.parent_agency_id = #{ordered_agency.parent_agency_id}"
            puts "DEBUG: OrderedAgency.grandparent_agency_id = #{ordered_agency.grandparent_agency_id}"
          end
          puts "DEBUG: Factory agency_id = #{factory.send(:agency_id)}"
          puts "DEBUG: Factory final_spend? = #{factory.send(:final_spend?)}"
          puts "DEBUG: Factory pre_or_post = #{factory.send(:pre_or_post)}"

          factory.create!
          expect(comments.first.comment_type).to eq(nbcu_post_registration)
        end
      end

      context 'when the agency has a grand parent agency & final spend of the amy record for that agency is true' do
        let!(:agency5) { create(:agency, parent_agency_id: parent_agency5.id) }
        let!(:parent_agency5) { create(:agency, parent_agency_id: grand_parent_agency5.id) }
        let!(:grand_parent_agency5) { create(:grandparent_agency) }
        let!(:deal5) { create(:deal, agency: agency5, marketplace: marketplace) }
        let!(:budget5) { create(:budget, deal: deal5) }
        let!(:budget_year5) { budget5.budget_year }
        let!(:agency_marketplace_year5i) {
          create(:agency_marketplace_year, agency: agency5,
                                           budget_year: budget_year5,
                                           marketplace: marketplace,
                                           final_spend: false)
        }
        let!(:agency_marketplace_year5ii) {
          create(:agency_marketplace_year, agency_id: grand_parent_agency5.id,
                                           budget_year: budget_year5,
                                           marketplace: marketplace,
                                           final_spend: true)
        }

        it 'creates a comment with nbcu_post_reg comment type depending on amy record of grand parent agency' do
          agency_marketplace_year5i
          agency_marketplace_year5ii
          AgencyDealCommentFactory.new(budget5, user, message).create!
          expect(comments.first.comment_type).to eq(nbcu_post_registration)
        end
      end

      context 'when the agency has a grand parent agency & final spend of the amy record for that agency is true' do
        let!(:agency6) { create(:agency, parent_agency_id: parent_agency6.id) }
        let!(:parent_agency6) { create(:agency, parent_agency_id: grand_parent_agency6.id) }
        let!(:grand_parent_agency6) { create(:grandparent_agency) }
        let!(:deal6) { create(:deal, agency: agency6, marketplace: marketplace) }
        let!(:budget6) { create(:budget, deal: deal6) }
        let!(:budget_year6) { budget6.budget_year }
        let!(:agency_marketplace_year6i) {
          create(:agency_marketplace_year, agency: agency6,
                                           budget_year: budget_year6,
                                           marketplace: marketplace,
                                           final_spend: false)
        }
        let!(:agency_marketplace_year6ii) {
          create(:agency_marketplace_year, agency_id: grand_parent_agency6.id,
                                           budget_year: budget_year6,
                                           marketplace: marketplace,
                                           final_spend: true)
        }

        it 'creates a comment with nbcu_post_reg comment type depending on amy record of grand parent agency' do
          agency_marketplace_year6i
          agency_marketplace_year6ii
          AgencyDealCommentFactory.new(budget6, user, message).create!
          expect(comments.first.comment_type).to eq(nbcu_post_registration)
        end
      end
    end
  end
end
