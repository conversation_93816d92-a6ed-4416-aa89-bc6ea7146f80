require 'spec_helper'
module PamClient
  describe GrandParentAgency do
    context 'relations' do
      xit { is_expected.to have_many(:parent_agencies) }
    end
  end

  describe 'generational methods' do
    describe 'a new object' do
      let(:subject) { build(:grandparent_agency) }

      it 'return the correct values' do
        expect(subject.parentless?).to be(true)
        expect(subject.orphan?).to be(true)
        expect(subject.parent?).to be(false)
        expect(subject.children).to be_empty
      end
    end

    describe 'with no children' do
      let(:subject) { create(:grandparent_agency) }

      it 'return the correct values' do
        expect(subject.parentless?).to be(true)
        expect(subject.orphan?).to be(true)
        expect(subject.parent?).to be(false)
        expect(subject.children).to be_empty
      end
    end

    describe 'with children' do
      let(:subject) { create(:grandparent_agency, :with_children) }

      it 'return the correct values' do
        expect(subject.parentless?).to be(true)
        expect(subject.orphan?).to be(false)
        expect(subject.parent?).to be(true)
        expect(subject.children).to_not be_empty
      end
    end
  end
end
