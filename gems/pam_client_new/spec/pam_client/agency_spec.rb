require 'spec_helper'

module PamClient
  describe Agency, type: :model do
    it_should_behave_like "a model with the has namespaced name mixin"
    it_should_behave_like "a model with typed id"
    it_should_behave_like "a stealth agency"

    describe "validations" do
      it "requires case insensitive unique value for agency_name" do
        should validate_presence_of(:agency_name)
        FactoryBot.create :agency, name: 'some agency'
        expect(Agency.new).to_not have_valid(:agency_name).when 'Some Agency'
      end

      it { is_expected.to have_db_index([:parent_agency_id, :agency_name]) }

      it { is_expected.to have_valid(:active).when(true) }
      it { is_expected.to have_valid(:active).when(false) }
      it { is_expected.not_to have_valid(:active).when(nil) }
      it { is_expected.to have_valid(:email).when(nil) }
      it { is_expected.to have_valid(:email).when("<EMAIL>") }
      it { is_expected.not_to have_valid(:email).when("foo@bar") }
    end

    describe "#name" do
      context "when agency has a parent agency" do
        let(:parent_agency) { FactoryBot.build(:parent_agency, agency_name: "PARENT AGENCY NAME") }
        let(:agency) { FactoryBot.build(:agency, parent_agency: parent_agency, agency_name: "THE AGENCY") }

        it "displays just the agency name" do
          expect(agency.name).to eq "THE AGENCY"
        end
      end

      context "when agency does not have a parent agency" do
        let(:agency) { FactoryBot.build(:agency, agency_name: "THE AGENCY", parent_agency: nil) }
        it "displays just the agency name" do
          expect(agency.name).to eq "THE AGENCY"
        end
      end
    end

    describe "#active" do
      it "defaults to true" do
        agency = Agency.new
        expect(agency.active?).to eq true
      end
    end

    describe ".active" do
      it "only returns active agencies" do
        active_agency = FactoryBot.create(:agency, active: true)
        inactive_agency = FactoryBot.create(:agency, active: false)

        expect(described_class.active.to_a).to eq [active_agency]
      end
    end

    describe '.with_deals' do
      it 'only returns agencies with deals having the passed ids' do
        agency_with_deal = FactoryBot.create(:agency)
        deal_1 = FactoryBot.create(:deal, agency_id: agency_with_deal.id, marketplace_id: 222)
        budget_1 = FactoryBot.create(:budget, deal_id: deal_1.id, budget_year_id: 333)

        agency_with_bad_deal = FactoryBot.create(:agency)
        deal_2 = FactoryBot.create(:deal, agency_id: agency_with_bad_deal.id, marketplace_id: 444)
        budget_2 = FactoryBot.create(:budget, deal_id: deal_2.id, budget_year_id: 555)

        FactoryBot.create(:agency)

        expect(described_class.with_deals(222, 333).to_a).to eq [agency_with_deal]
      end

      it 'contains distinct agencies' do
        agency_with_deal = FactoryBot.create(:agency)
        deal_1 = FactoryBot.create(:deal, agency_id: agency_with_deal.id, marketplace_id: 222)
        budget_1 = FactoryBot.create(:budget, deal_id: deal_1.id, budget_year_id: 333)
        deal_2 = FactoryBot.create(:deal, agency_id: agency_with_deal.id, marketplace_id: 222)
        budget_2 = FactoryBot.create(:budget, deal_id: deal_2.id, budget_year_id: 333)

        expect(described_class.with_deals(222, 333).to_a).to eq [agency_with_deal]
      end
    end

    # describe ".for_user" do
    #   context "for a user with assignments" do
    #     it "returns the agencies for those assignments, and not others" do
    #       assigned_agency = FactoryBot.create(:agency)
    #       unassigned_agency = FactoryBot.create(:agency)
    #
    #       user = FactoryBot.create(:user)
    #
    #       FactoryBot.create(:assignment, user: user, agency: assigned_agency)
    #
    #       results = Agency.for_user(user)
    #
    #       expect(results).to include assigned_agency
    #       expect(results).not_to include unassigned_agency
    #     end
    #   end
    #
    #   context "for a user with an 'all' assignment" do
    #     it "returns all agencies" do
    #       assigned_agency = FactoryBot.create(:agency)
    #       unassigned_agency = FactoryBot.create(:agency)
    #
    #       user = FactoryBot.create(:user)
    #
    #       FactoryBot.create(:assignment, user: user, agency: assigned_agency)
    #       FactoryBot.create(:assignment, :for_all_agencies, user: user)
    #
    #       results = Agency.for_user(user)
    #
    #       expect(results).to include assigned_agency, unassigned_agency
    #     end
    #   end
    # end

    # describe ".for_user_on_property" do
    #   it "returns the agencies assigned to a user on the given property" do
    #     this_agency = FactoryBot.create(:agency)
    #     that_agency = FactoryBot.create(:agency)
    #
    #     user = FactoryBot.create(:user)
    #
    #     this_property = FactoryBot.create(:property)
    #     that_property = FactoryBot.create(:property)
    #
    #     FactoryBot.create(:assignment, user: user, agency: this_agency, property: this_property)
    #     FactoryBot.create(:assignment, user: user, agency: that_agency, property: that_property)
    #
    #     expect(Agency.for_user_on_property user, this_property).to include this_agency
    #     expect(Agency.for_user_on_property user, this_property).to_not include that_agency
    #   end
    #
    #   context "for a user with an all-properties assignment" do
    #     it "includes the agency with the all-properties assignment" do
    #       this_agency = FactoryBot.create(:agency)
    #       this_property = FactoryBot.create(:property)
    #
    #       user = FactoryBot.create(:user)
    #
    #       FactoryBot.create(:assignment, user: user, agency: this_agency, property: nil)
    #
    #       expect(Agency.for_user_on_property user, this_property).to include this_agency
    #     end
    #   end
    # end

    describe ".without_parents" do
      # Use the existing -1 blank agency instead of trying to create a new one
      let(:blank_agency) { PamClient::BaseAgency.unscoped.find(-1) }
      let!(:included_parentless_agency) { FactoryBot.create(:agency, name: 'Parentless Agency', parent_agency_id: -1) }
      let!(:included_parent_agency) { FactoryBot.create(:parent_agency, name: 'Parentless top level Agency') }
      let!(:excluded_agency_with_parent) { FactoryBot.create(:agency, name: 'Agency with Parent', parent_agency: included_parent_agency) }

      it 'returns agencies which have a parent_agency_id of -1' do
        expect(BaseAgency.without_parents).to include included_parent_agency
      end

      it 'returns agencies which have a agency_type of "ParentAgency"' do
        expect(BaseAgency.without_parents).to include included_parentless_agency
      end

      it 'does not include agencies with parents' do
        expect(BaseAgency.without_parents).to_not include excluded_agency_with_parent
      end

      it 'does not include blank agencies' do
        expect(BaseAgency.without_parents).to_not include blank_agency
      end
    end

    describe ".autocomplete" do
      it "with search parameters returns a collection of agencies" do
        agency_one   = FactoryBot.create(:agency, agency_name: "4D Agency")
        agency_two   = FactoryBot.create(:agency, agency_name: "Mediavest")
        agency_three = FactoryBot.create(:agency, agency_name: "Urban Media Agency")
        agency_four  = FactoryBot.create(:agency, agency_name: "Group M")
        scope_results = [agency_two, agency_three]
        search_params = "me"

        expect(described_class.autocomplete(search_params, "agency_name")).to match_array scope_results
      end
    end

    describe "create or edit agency with blank parent_agency" do
      context "when agency does not have a parent agency" do
        let(:agency) { FactoryBot.create(:agency, agency_name: "THE AGENCY", parent_agency: nil) }
        it "should assign -1 to parent_agency_id" do
          expect(agency.parent_agency_id).to eq -1
        end
      end
    end
  end
end
