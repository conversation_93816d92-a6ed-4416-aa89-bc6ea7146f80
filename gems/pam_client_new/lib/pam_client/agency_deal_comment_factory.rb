# frozen_string_literal: true

module <PERSON><PERSON>lient
  # Class for creating new AgencyDealComments
  class AgencyDealCommentFactory
    attr_reader :budget, :budget_year, :comment_type, :deal, :message, :user

    # @belongs_to AgencyDealCommentFactory
    # @name initialize
    # @description
    #   Builds a new instance of AgencyDealCommentFactory
    #
    # @param [Budget] budget
    # @param [User] user
    # @param [String]
    # @return [AgencyDealCommentFactory]
    def initialize(budget, user, message)
      @budget = budget
      @budget_year = @budget.budget_year
      @deal = @budget.deal
      @user = user
      @comment_type = comment_type_setter
      @message = message
    end

    # @belongs_to AgencyDealCommentFactory
    # @name create!
    # @description
    #   Creates a new instance of AgencyDealComment, or throws an Exception
    #   if a valid instance cannot be created from the supplied params
    #
    # @return [True || Exception]
    def create!
      adc = AgencyDealComment.new(
        source_id: @budget.id,
        app_user_id: @user.id,
        message: @message,
        comment_type: @comment_type,
        commenter_name: user.full_name
      )
      adc.save!
    end

    private

    def comment_type_setter
      type_method = "nbcu_#{pre_or_post}_registration".to_sym
      CommentType.public_send(type_method) if CommentType.respond_to?(type_method)
    end

    def agency_marketplace_year
      AgencyMarketplaceYear
        .where(
          budget_year: @budget.budget_year,
          agency_id:,
          marketplace: @deal.marketplace
        )
        .first_or_create
    end

    def ordered_agency
      OrderedAgency.where(agency_id: @deal.agency.id).first
    end

    def agency_id
      if ordered_agency.grandparent_agency_id.present? && ordered_agency.grandparent_agency_id != -1
        ordered_agency.grandparent_agency_id
      elsif ordered_agency.parent_agency_id.present? && ordered_agency.parent_agency_id != -1
        ordered_agency.parent_agency_id
      else
        ordered_agency.agency_id
      end
    end

    def final_spend?
      agency_marketplace_year.final_spend
    end

    def pre_or_post
      final_spend? ? 'post' : 'pre'
    end
  end
end
