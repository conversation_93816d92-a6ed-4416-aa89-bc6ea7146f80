### Class to deal with stealth mode preference for each agency
module PamClient
  class AgencyStealthModePref < ApplicationRecord
    self.table_name = 'agency_stealth_mode_pref'
    self.primary_key = 'agency_stealth_mode_pref_id'

    belongs_to :base_agency, foreign_key: :agency_id
    validates :agency_id, presence: true, uniqueness: true

    # Provide agency method as alias to base_agency association
    alias_method :agency, :base_agency
  end
end
