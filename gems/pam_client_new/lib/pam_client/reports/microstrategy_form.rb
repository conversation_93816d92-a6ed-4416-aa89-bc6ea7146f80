# lib/pam_client/reports/microstrategy_form.rb
module PamClient
  module Reports
    ### Class for Microstrategy Form
    class MicrostrategyForm < ApplicationRecord
      self.table_name = 'microstrategy_form'
      self.primary_key = 'microstrategy_form_id'

      # Include concerns AFTER table setup
      # Note: HasNamespacedName removed - this model doesn't have microstrategy_form_name column
      include Concerns::HasAdditionalFilters
      include Default<PERSON>arketplaceFilter

      belongs_to :budget_year, foreign_key: :budget_year_id
      belongs_to :calendar_year, foreign_key: :calendar_year_id
      belongs_to :microstrategy_report, foreign_key: :microstrategy_report_id

      has_one :saved_template, class_name: 'PamClient::Reports::SavedTemplate',
                               foreign_key: 'form_id',
                               dependent: :destroy

      # this relationship needed for non-polymorphic access of filters from
      # MicrostrategyForm, as opposed to that included in HasAdditionalFilters
      #
      has_many :additional_filters,
               class_name: '::PamClient::Reports::AdditionalFilter',
               foreign_key: 'form_id',
               dependent: :destroy

      validates :microstrategy_report_id, presence: true

      delegate :name, :document_id, to: :microstrategy_report, prefix: 'report'
      delegate :report_type, to: :microstrategy_report
      alias report_title report_name
    end
  end
end
