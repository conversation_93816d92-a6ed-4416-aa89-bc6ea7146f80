module PamClient
  # validations, methods for portal term
  class PortalTerm < ApplicationRecord
    # Note: HasNamespacedName removed - this model uses portal_checklist_task, not portal_term_name column
    include Concerns::SharedScope
    include Concerns::DropdownValue

    self.table_name = 'portal_term'
    self.primary_key = 'portal_term_id'

    belongs_to :budget_year
    belongs_to :marketplace
    belongs_to :portal_term_type
    has_many :agency_portal_terms
    has_many :agencies, through: :agency_portal_terms

    validates :portal_checklist_task, :budget_year_id, :marketplace_id, :display_order,
              presence: true

    COLUMN_NAMES_FOR_JOIN_QUERY =
      "portal_term.portal_term_id AS pc_id,
       portal_term.portal_checklist_task AS task_name,
       portal_term.display_order, apc.*, cm.updated_at AS commented_on,
       ae.app_user_id AS ae_id, cm.message, cm.app_user_id AS commentor_id,
       (usr.first_name || ' ' || usr.last_name) AS commentor".freeze

    JOIN_QUERY_FOR_TERM_DATA =
      "LEFT OUTER JOIN agency_portal_term apc ON
       portal_term.portal_term_id = apc.portal_term_id AND
       apc.agency_id = ? LEFT OUTER JOIN app_user ae ON apc.completed_by =
       ae.app_user_id LEFT OUTER JOIN commentary cm ON apc.agency_portal_term_id
       = cm.source_id AND cm.commentary_type = 'AgencyPortalTermComment' LEFT
       OUTER JOIN app_user usr ON cm.app_user_id = usr.app_user_id".freeze

    def portal_term_name
      portal_checklist_task
    end

    def name
      portal_checklist_task
    end

    # Override as_json to include 'name' field (replaces HasNamespacedName functionality)
    def as_json(options = nil)
      super.tap do |hash|
        hash.merge!('name' => portal_checklist_task)
      end
    end

    # delegation
    alias task portal_term_name

    # Class method to find by name (replaces HasNamespacedName functionality)
    def self.find_by_name(name)
      find_by_portal_checklist_task(name)
    end

    # Scope for fetching protal terms for a given budget year, marketplace and portal term type
    scope :for_budgetyear_marketplace_and_portal_type, (lambda do |budget_year_id, marketplace_id, portal_term_type_id|
      where(budget_year_id:, marketplace_id:, portal_term_type_id:)
        .order(:display_order, :portal_checklist_task)
    end)

    # Fetches the portal terms for a budgetyear and marketplace
    # combination as an array of hashes
    #
    # @param budget_year_id [int] budget_year_id
    # @param marketplace_id [int] marketplace_id
    # @param portal_term_type_id [int] portal_term_type_id
    # @return [Array] array of portal terms hashes with id & task name
    def self.portal_terms(budget_year_id, marketplace_id, portal_term_type_id)
      [].tap do |ary|
        for_budgetyear_marketplace_and_portal_type(budget_year_id, marketplace_id, portal_term_type_id)
          .each do |portal_term|
          ary << portal_term.extract(%i[id task])
        end
      end
    end

    # Fetches term data by joining portal_term, agency_portal_term
    #
    # @param options [Hash] hash of agency_id, budget_year_id, app_user_id, marketplace_id
    # @return [Array<Hash>]
    def self.get_term_data(options)
      result_set = joins(sanitize_sql_array([JOIN_QUERY_FOR_TERM_DATA, options.agency_id]))
                   .where(budget_year_id: options.b_year_id, marketplace_id: options.mp_id)
                   .select(COLUMN_NAMES_FOR_JOIN_QUERY)
                   .order('display_order, task_name ASC')
      build_term_data(result_set)
    end

    # Prepares term data array
    #
    # @param result_set [Array<Hash>] Records from portal_term,
    #   agency_portal_term on agency_id and portal_term_id join
    # @return [Array<Hash>]
    def self.build_term_data(result_set)
      portal_terms = []
      result_set.each { |item| portal_terms << term_item(item) }
      portal_terms
    end

    # Builds and returns hash of term item from result set item
    #
    # @param item [Hash]
    # @return [Hash]
    def self.term_item(item)
      hsh = { id: item.pc_id, task_name: item.task_name }
      hsh.store(:agency_pc, term_item_agency_pc(item))
      hsh.store(:comments, term_item_comment(item))
      hsh
    end

    # Builds and returns hash of agency_portal_term data from result set item
    #
    # @param item [Hash]
    # @return [Hash]
    def self.term_item_agency_pc(item)
      c_val = item.completed
      { id: item.agency_portal_term_id,
        completed: c_val ? !c_val.zero? : c_val, # coerces SQL result TinyInt (0/1) to Boolean
        completed_on: item.completed_on ? item.completed_on.strftime('%m/%d/%Y') : '',
        completed_by: item.completed_by }
    end

    # Builds and returns hash of comment data from result set item
    #
    # @param item [Hash]
    # @return [Hash]
    def self.term_item_comment(item)
      { message: item.message,
        commentor: item.commentor,
        commented_on: item.commented_on ? item.commented_on.strftime('%B %d %Y %l:%M %p %Z') : '' }
    end

    # Converts date string to date in '%Y-%m-%dT00:00:00.000Z' format
    #
    # @param date [String] Date string in 'mm/dd/yyyy' format
    # @return [Date] In '%Y-%m-%dT00:00:00.000Z' format
    def self.format_date(date)
      Date.strptime(date, '%m/%d/%Y').to_date.strftime('%Y-%m-%dT00:00:00.000Z')
    end
  end
end
