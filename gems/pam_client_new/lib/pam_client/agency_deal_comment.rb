# frozen_string_literal: true

module Pam<PERSON>lient
  # Class for Agency Deal comments
  class AgencyDealComment < Comment
    belongs_to :budget, foreign_key: 'source_id', primary_key: 'budget_id'
    belongs_to :budget_year
    has_one :deal, through: :budget
    belongs_to :user, -> { unscope(:where) }, class_name: 'User', foreign_key: 'app_user_id'
    belongs_to :comment_type

    validates :user, presence: true

    before_create :set_comment_type

    # alias attribute for creating comments hash
    alias_attribute :budget_id, :source_id

    # Pre-Final-Spend Comments (AG Users)
    scope :ag_pre_reg,
          -> { where(comment_type_id: PamClient::CommentType.ag_pre_registration.id) }

    # Post-Final-Spend Comments (AG Users)
    scope :ag_post_reg,
          -> { where(comment_type_id: PamClient::CommentType.ag_post_registration.id) }

    # Pre-Final-Spend Comments (NBCU Portal Users)
    scope :nbcu_pre_reg,
          -> { where(comment_type_id: PamClient::CommentType.nbcu_pre_registration.id) }

    # Post-Final-Spend Comments (NBCU Portal Users)
    scope :nbcu_post_reg,
          -> { where(comment_type_id: PamClient::CommentType.nbcu_post_registration.id) }

    # AG comments only
    scope :ag, -> { where(comment_type: PamClient::CommentType.ag) }

    # NBCU comments only
    scope :nbcu, -> { where(comment_type: PamClient::CommentType.nbcu) }

    # Unread comments
    scope :unread, -> { where(unread: true) }

    # Unread AG comments
    scope :unread_ag, -> { ag.unread }

    # Unread NBCU comments
    scope :unread_nbcu, -> { nbcu.unread }

    # Comments not submitted to AG
    scope :unsubmitted, -> { where(submitted_to_ag: false) }

    delegate :comment_type_name, to: :comment_type, prefix: false

    def self.for_budget_id(budget_id)
      includes(:user, :comment_type)
        .where(source_id: budget_id)
        .order(:created_at)
    end

    # @description
    #   includes updated_at, because update_all does not update timestamps by default
    #   uses sql because update_all does not typecast, values are passed as-is
    #
    # @return [Boolean] whether or not the update completed succesfully
    def self.mark_ag_as_read
      unread_ag.update_all(['unread = 0, updated_at = ?', DateTime.now.strftime('%Y-%m-%d %H:%M:%S')])
    end

    # Sets current budget year for agency deal comments
    #
    # @return [AgencyDealComment] AgencyDealComment object with current budget year
    def init_defaults
      self.budget_year = current_budget.budget_year
    end

    # Updates the comments status which are submitted to AG
    #
    # @param budget_ids [Array] array of budget ids
    # @return [AgencyDealComment] updated comments
    def self.mark_submitted_for_ag(budget_ids)
      split_ids_into_arrays(budget_ids).each do |budget_arry|
        where(source_id: budget_arry).update_all(submitted_to_ag: true)
      end
    end

    # creates a two dimensional array of budget ids
    # with each sub array consisting of 600 elements.
    #
    # @param ids [Array] Array of budget ids
    # @return [Array] two dimensional array of budget id grouped in 600 elements
    def self.split_ids_into_arrays(ids)
      ids.each_slice(600).to_a
    end

    # Create a comments hash in json for message queue
    #
    # @param budget_ids [Array] Array of budgets associated to parent agency
    # @return [JSON] comments JSON object
    def self.comments_json(ids)
      budget_ids = split_ids_into_arrays(ids)
      { comments: extracted_comments(budget_ids) }.to_json
    end

    # Builds an array of hashes with comments
    # attributes required for sending data to AG
    # The budget_ids param is a two dimensional array
    # with each sub array having 600 budget ids
    #
    # @param budget_ids [Array] two dimensional array of budget ids
    # @return [Array] Array of hash with each hash contain extract comment attributes
    def self.extracted_comments(budget_ids)
      [].tap do |ary|
        budget_comments(budget_ids).each do |comment|
          ary << comment.extract_comment_hash
        end
      end.flatten
    end

    # fetches the comments associated to budgets
    # The budget_ids param is a two dimensional array
    # with each sub array having 600 budget ids
    #
    # @param budget_ids [Array] two dimensional array of budget ids
    # @return [Array] array of unsubmitted AgencyDealComment objects
    def self.budget_comments(budget_ids)
      comments_array = []
      budget_ids.each do |budget_arry|
        comments_array << where(source_id: budget_arry)
                          .order(created_at: :asc)
                          .unsubmitted
      end
      comments_array.flatten
    end

    # Extract the required attributes
    # from Comment object for AG
    #
    # @param user [User] User Object
    # @return [Hash] hash with comment attributes required for AG
    def extract_comment_hash
      extract(%i[budget_id message created_at updated_at
                 comment_type_name unread commenter_name])
    end

    private

    def set_comment_type
      return if comment_type.present?

      type_method = "nbcu_#{pre_or_post}_registration".to_sym
      self.comment_type = PamClient::CommentType.public_send(type_method) if PamClient::CommentType.respond_to?(type_method)
    end

    def pre_or_post
      final_spend? ? 'post' : 'pre'
    end

    def final_spend?
      agency_marketplace_year.final_spend
    end

    def agency_marketplace_year
      PamClient::AgencyMarketplaceYear
        .where(
          budget_year:,
          agency_id:,
          marketplace: deal.marketplace
        )
        .first_or_create
    end

    def ordered_agency
      PamClient::OrderedAgency.where(agency_id: deal.agency.id).first
    end

    def agency_id
      if ordered_agency.grandparent_agency_id.present? && ordered_agency.grandparent_agency_id != -1
        ordered_agency.grandparent_agency_id
      elsif ordered_agency.parent_agency_id.present? && ordered_agency.parent_agency_id != -1
        ordered_agency.parent_agency_id
      else
        ordered_agency.agency_id
      end
    end
  end
end
