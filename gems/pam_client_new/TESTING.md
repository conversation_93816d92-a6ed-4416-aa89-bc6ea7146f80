# Testing Guide for pam_client_new

## Running Only Failed Specs

The pam_client_new project is configured to track test failures and allow you to rerun only the specs that failed in the previous run. This is extremely useful for iterative development and debugging.

### Quick Start

```bash
# Run only failed specs (most common usage)
./bin/rspec-failures

# Or use RSpec directly
bundle exec rspec --only-failures
```

### Available Commands

```bash
# Run only failed specs
./bin/rspec-failures

# Run all specs and track failures for next time
./bin/rspec-failures --all

# Clear failure tracking (start fresh)
./bin/rspec-failures --clear

# Show help
./bin/rspec-failures --help
```

### How It Works

1. **First Run**: When you run specs for the first time, <PERSON><PERSON> creates a `spec/examples.txt` file that tracks the status of each test.

2. **Subsequent Runs**: When you use `--only-failures`, RSpec only runs the tests that failed in the previous run.

3. **Automatic Tracking**: Every time you run specs, the failure status is automatically updated.

### Example Workflow

```bash
# 1. Run all specs to establish baseline
./bin/rspec-failures --all

# 2. If some tests fail, fix the code and run only failed tests
./bin/rspec-failures

# 3. Keep iterating on failed tests until they pass
./bin/rspec-failures

# 4. Once all tests pass, run full suite to make sure nothing broke
./bin/rspec-failures --all
```

### Manual RSpec Usage

You can also use RSpec directly with these options:

```bash
# Run only failed specs
bundle exec rspec --only-failures

# Run all specs with documentation format
bundle exec rspec --format documentation

# Run specific spec file
bundle exec rspec spec/pam_client/finance_unit_spec.rb

# Run specific test
bundle exec rspec spec/pam_client/finance_unit_spec.rb:19
```

### Configuration

The failure tracking is configured in `spec/spec_helper.rb`:

```ruby
RSpec.configure do |config|
  # Enable --only-failures feature to rerun only failed specs
  config.example_status_persistence_file_path = "spec/examples.txt"
end
```

The `spec/examples.txt` file is automatically ignored by git (listed in `.gitignore`).

## Database Setup

The test suite uses Oracle database with the following configuration:
- **Host**: localhost (or SMS_DB_HOST env var)
- **Username**: smsdbo (or SMS_DB_USERNAME env var)  
- **Password**: smsdbo123 (or SMS_DB_PASSWORD env var)
- **Database**: /ORCLPDB1 (or SMS_DB_DATABASE env var)
- **Port**: 1521 (or SMS_DB_PORT env var)

### Environment Variables

You can override database settings with environment variables:
```bash
export SMS_DB_HOST=your-oracle-host
export SMS_DB_USERNAME=your-username
export SMS_DB_PASSWORD=your-password
export SMS_DB_DATABASE=/YOUR_PDB
export SMS_DB_PORT=1521
```

## Test Categories

- **Unit Tests**: Individual model and concern tests
- **Integration Tests**: Tests that verify interactions between components
- **Shared Examples**: Reusable test patterns (like `finance_actual_update`)

## Common Issues

### Database Connection Issues
If you see database connection errors, make sure:
1. Oracle database is running
2. Connection parameters are correct
3. Database user has proper permissions

### Constraint Validation Issues
The test suite can run with database constraints enabled or disabled:
```bash
# Run with constraints enabled (more realistic)
DB_CONSTRAINTS=true bundle exec rspec

# Run with constraints disabled (faster, more forgiving)
bundle exec rspec
```
